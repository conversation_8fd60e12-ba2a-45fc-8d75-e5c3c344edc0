# Cosmic功能点转需求说明文档工具

## 功能说明

这个工具可以将Cosmic功能点Excel文件转换为标准的需求说明Markdown文档。

## 主要特性

- ✅ 自动读取Excel文件中的Cosmic功能点数据
- ✅ 按照层级结构组织内容（一级模块→二级模块→三级模块→功能用户需求）
- ✅ 生成符合规范的需求说明Markdown文档
- ✅ 包含触发事件、功能过程和子过程描述
- ✅ 提供详细的统计信息
- ✅ 支持命令行参数

## 使用方法

### 1. 基本使用

```bash
# 使用默认Excel文件
python cosmic_to_requirement.py

# 指定Excel文件路径
python cosmic_to_requirement.py path/to/your/cosmic_file.xlsx
```

### 2. 输入文件要求

Excel文件应包含以下列：
- 一级模块
- 二级模块  
- 三级模块
- 功能用户需求
- 触发事件
- 功能过程
- 子过程描述

### 3. 输出格式

生成的Markdown文档格式如下：

```markdown
## 1 业务功能需求
### 1.1 一级模块名称
#### 1.1.1 二级模块名称
##### 1.1.1.1 三级模块名称
  - 功能用户需求描述
    触发事件为xxx，功能过程为xxx，包含以下子过程：
    
    1.子过程描述1
    2.子过程描述2
    3.子过程描述3
    ...
```

## 输出文件

- 输出文件名：`{原Excel文件名}_需求说明.md`
- 输出位置：与Excel文件相同目录
- 编码格式：UTF-8

## 统计信息

工具会自动提供以下统计信息：
- 总行数
- 功能需求数量
- 子过程数量

## 示例

```bash
$ python cosmic_to_requirement.py ../cosmic_results/allcosmic-20250703.xlsx

成功读取Excel文件: ../cosmic_results/allcosmic-20250703.xlsx
数据行数: 2051
📝 需求说明文档已保存到: ..\cosmic_results\allcosmic-20250703_需求说明.md
📊 统计信息:
   - 总行数: 97
   - 功能需求数: 9
   - 子过程数: 39

✅ 转换完成！
📄 输出文件: ..\cosmic_results\allcosmic-20250703_需求说明.md
📊 Excel文件: ../cosmic_results/allcosmic-20250703.xlsx
```

## 依赖库

```bash
pip install pandas openpyxl
```

## 注意事项

1. 确保Excel文件格式正确，包含必要的列
2. 工具会自动检测Excel文件的标题行位置
3. 生成的文档会覆盖同名的现有文件
4. 支持中文文件名和路径

## 错误处理

- 如果Excel文件不存在，会显示错误信息和使用说明
- 如果Excel文件格式不正确，会尝试不同的标题行位置
- 如果转换失败，会显示详细的错误信息

## 版本信息

- 版本：1.0
- 作者：AI Assistant
- 更新日期：2025-01-04
