#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设计文档分析器
分析设计文档并生成对应的COSMIC功能点生成器脚本
"""

import os
import re
import json
from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class FunctionalProcess:
    """功能过程数据结构"""
    name: str
    user: str
    requirement: str
    trigger: str
    subprocesses: List[Dict[str, Any]]

@dataclass
class SystemInfo:
    """系统信息数据结构"""
    subsystem: str
    module_level2: str
    module_level3: str
    platforms: List[str]

class DesignDocAnalyzer:
    """设计文档分析器"""
    
    def __init__(self):
        self.system_info = None
        self.functional_processes = []
    
    def analyze_markdown_doc(self, doc_path: str) -> Dict[str, Any]:
        """分析Markdown设计文档"""
        
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取系统信息
        self.system_info = self._extract_system_info(content)
        
        # 提取功能过程
        self.functional_processes = self._extract_functional_processes(content)
        
        return {
            'system_info': self.system_info,
            'functional_processes': self.functional_processes
        }
    
    def _extract_system_info(self, content: str) -> SystemInfo:
        """从文档中提取系统信息"""
        
        # 提取标题作为系统名称
        title_match = re.search(r'^#\s+(.+)', content, re.MULTILINE)
        title = title_match.group(1) if title_match else "未知系统"
        
        # 根据标题推断系统信息
        if "和包支付" in title or "移动支付" in title:
            return SystemInfo(
                subsystem="智慧社区",
                module_level2="支付管理",
                module_level3="和包支付集成",
                platforms=["移动端", "PC端"]
            )
        elif "第三方商城" in title or "积分" in title:
            return SystemInfo(
                subsystem="智慧社区", 
                module_level2="积分兑换",
                module_level3="第三方商城集成",
                platforms=["移动端", "PC端"]
            )
        else:
            # 默认系统信息
            return SystemInfo(
                subsystem="智慧社区",
                module_level2="业务管理",
                module_level3=title.replace("设计文档", "").replace("集成", "").strip(),
                platforms=["移动端", "PC端"]
            )
    
    def _extract_functional_processes(self, content: str) -> List[FunctionalProcess]:
        """从文档中提取功能过程"""
        
        processes = []
        
        # 查找业务流程或功能模块章节
        sections = re.findall(r'###?\s+(.+?)(?=\n)', content)
        
        for section in sections:
            if any(keyword in section for keyword in ['功能', '流程', '处理', '管理', '查询', '配置']):
                # 为每个功能创建基础的功能过程
                process = self._create_basic_functional_process(section)
                if process:
                    processes.append(process)
        
        return processes
    
    def _create_basic_functional_process(self, section_name: str) -> FunctionalProcess:
        """根据章节名称创建基础功能过程"""
        
        # 根据关键词推断功能用户和需求
        if "支付" in section_name:
            user = "用户"
            requirement = f"用户{section_name}"
            trigger = "用户发起支付操作"
        elif "查询" in section_name:
            user = "管理员" if "管理" in section_name else "用户"
            requirement = f"{user}{section_name}"
            trigger = f"{user}发起查询请求"
        elif "配置" in section_name or "管理" in section_name:
            user = "管理员"
            requirement = f"管理员{section_name}"
            trigger = "管理员修改配置"
        elif "通知" in section_name or "回调" in section_name:
            user = "第三方系统"
            requirement = f"第三方系统{section_name}"
            trigger = "第三方系统发送通知"
        else:
            user = "用户"
            requirement = f"用户{section_name}"
            trigger = "用户发起操作"
        
        # 创建基础子过程
        subprocesses = self._create_basic_subprocesses(section_name)
        
        return FunctionalProcess(
            name=section_name,
            user=user,
            requirement=requirement,
            trigger=trigger,
            subprocesses=subprocesses
        )
    
    def _create_basic_subprocesses(self, process_name: str) -> List[Dict[str, Any]]:
        """创建基础子过程"""
        
        # 根据功能类型创建不同的子过程模板
        if "支付" in process_name:
            return [
                {
                    'description': '接收支付请求',
                    'move_type': 'E',
                    'data_group': '支付请求',
                    'data_attributes': '用户ID,支付金额,订单号',
                    'context': '支付请求',
                    'cfp': 1
                },
                {
                    'description': '获取用户账户',
                    'move_type': 'R',
                    'data_group': '用户账户',
                    'data_attributes': '用户ID,账户余额,账户状态',
                    'context': '用户账户',
                    'cfp': 1
                },
                {
                    'description': '存储支付记录',
                    'move_type': 'W',
                    'data_group': '支付记录',
                    'data_attributes': '支付ID,用户ID,支付金额,支付时间',
                    'context': '支付记录',
                    'cfp': 1
                },
                {
                    'description': '传输支付结果',
                    'move_type': 'X',
                    'data_group': '支付结果',
                    'data_attributes': '支付状态,支付时间,交易流水号',
                    'context': '支付结果',
                    'cfp': 1
                }
            ]
        elif "查询" in process_name:
            return [
                {
                    'description': '接收查询请求',
                    'move_type': 'E',
                    'data_group': '查询请求',
                    'data_attributes': '查询条件,查询时间,用户ID',
                    'context': '查询请求',
                    'cfp': 1
                },
                {
                    'description': '获取查询数据',
                    'move_type': 'R',
                    'data_group': '查询数据',
                    'data_attributes': '数据ID,数据内容,数据状态',
                    'context': '查询数据',
                    'cfp': 1
                },
                {
                    'description': '传输查询结果',
                    'move_type': 'X',
                    'data_group': '查询结果',
                    'data_attributes': '结果总数,结果明细,分页信息',
                    'context': '查询结果',
                    'cfp': 1
                }
            ]
        elif "配置" in process_name:
            return [
                {
                    'description': '接收配置更新请求',
                    'move_type': 'E',
                    'data_group': '配置更新请求',
                    'data_attributes': '配置项,配置值,操作人员',
                    'context': '配置更新请求',
                    'cfp': 1
                },
                {
                    'description': '获取当前配置',
                    'move_type': 'R',
                    'data_group': '当前配置',
                    'data_attributes': '配置项,当前值,配置状态',
                    'context': '当前配置',
                    'cfp': 1
                },
                {
                    'description': '存储新配置',
                    'move_type': 'W',
                    'data_group': '新配置',
                    'data_attributes': '配置项,新值,更新时间,操作人员',
                    'context': '新配置',
                    'cfp': 1
                },
                {
                    'description': '传输配置更新结果',
                    'move_type': 'X',
                    'data_group': '配置更新结果',
                    'data_attributes': '更新状态,更新时间,配置详情',
                    'context': '配置更新结果',
                    'cfp': 1
                }
            ]
        else:
            # 通用子过程模板
            return [
                {
                    'description': '接收业务请求',
                    'move_type': 'E',
                    'data_group': '业务请求',
                    'data_attributes': '请求ID,请求内容,请求时间',
                    'context': '业务请求',
                    'cfp': 1
                },
                {
                    'description': '获取业务数据',
                    'move_type': 'R',
                    'data_group': '业务数据',
                    'data_attributes': '数据ID,数据内容,数据状态',
                    'context': '业务数据',
                    'cfp': 1
                },
                {
                    'description': '存储处理结果',
                    'move_type': 'W',
                    'data_group': '处理结果',
                    'data_attributes': '结果ID,处理状态,处理时间',
                    'context': '处理结果',
                    'cfp': 1
                },
                {
                    'description': '传输业务响应',
                    'move_type': 'X',
                    'data_group': '业务响应',
                    'data_attributes': '响应状态,响应内容,响应时间',
                    'context': '业务响应',
                    'cfp': 1
                }
            ]
    
    def generate_generator_script(self, output_path: str, class_name: str) -> str:
        """生成对应的生成器脚本"""
        
        if not self.system_info or not self.functional_processes:
            raise ValueError("请先分析设计文档")
        
        script_content = self._create_generator_script_content(class_name)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        return output_path
    
    def _create_generator_script_content(self, class_name: str) -> str:
        """创建生成器脚本内容"""
        
        # 生成导入和类定义
        script = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
{self.system_info.module_level3} COSMIC功能点生成器
自动从设计文档生成
"""

import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class {class_name}(BaseCosmicGenerator):
    """{self.system_info.module_level3}COSMIC功能点生成器"""
    
    def __init__(self):
        super().__init__(
            subsystem="{self.system_info.subsystem}",
            module_level2="{self.system_info.module_level2}", 
            module_level3="{self.system_info.module_level3}"
        )
    
    def generate_functional_processes(self):
        """生成{self.system_info.module_level3}的所有功能过程"""
        
        platforms = {self.system_info.platforms}
        
        for platform in platforms:
'''
        
        # 生成每个功能过程
        for i, process in enumerate(self.functional_processes, 1):
            script += f'''
            # {i}. {process.name}
            self.add_functional_process(
                platform=platform,
                functional_user="{process.user}",
                user_requirement="{process.requirement}",
                trigger_event="{process.trigger}",
                process_name=f"{{platform}}{process.name}",
                subprocesses=[
'''
            
            # 生成子过程
            for subprocess in process.subprocesses:
                script += f'''                    {{
                        'description': '{subprocess["description"]}',
                        'move_type': '{subprocess["move_type"]}',
                        'data_group': '{subprocess["data_group"]}',
                        'data_attributes': '{subprocess["data_attributes"]}',
                        'context': '{subprocess["context"]}',
                        'cfp': {subprocess["cfp"]}
                    }},
'''
            
            script += '''                ]
            )'''
        
        return script
    
    def save_analysis_result(self, output_path: str):
        """保存分析结果为JSON"""
        
        result = {
            'system_info': {
                'subsystem': self.system_info.subsystem,
                'module_level2': self.system_info.module_level2,
                'module_level3': self.system_info.module_level3,
                'platforms': self.system_info.platforms
            },
            'functional_processes': [
                {
                    'name': fp.name,
                    'user': fp.user,
                    'requirement': fp.requirement,
                    'trigger': fp.trigger,
                    'subprocesses': fp.subprocesses
                }
                for fp in self.functional_processes
            ]
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        return output_path


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="设计文档分析器")
    parser.add_argument("--doc", "-d", required=True, help="设计文档路径")
    parser.add_argument("--output", "-o", help="输出生成器脚本路径")
    parser.add_argument("--class-name", "-c", help="生成器类名")
    parser.add_argument("--analysis", "-a", help="保存分析结果JSON路径")
    
    args = parser.parse_args()
    
    analyzer = DesignDocAnalyzer()
    
    try:
        # 分析文档
        print(f"正在分析设计文档: {args.doc}")
        result = analyzer.analyze_markdown_doc(args.doc)
        
        print(f"✅ 分析完成!")
        print(f"📋 系统: {result['system_info'].subsystem} - {result['system_info'].module_level3}")
        print(f"🔧 功能过程数量: {len(result['functional_processes'])}")
        
        # 生成生成器脚本
        if args.output and args.class_name:
            script_path = analyzer.generate_generator_script(args.output, args.class_name)
            print(f"📄 生成器脚本已保存: {script_path}")
        
        # 保存分析结果
        if args.analysis:
            analysis_path = analyzer.save_analysis_result(args.analysis)
            print(f"📊 分析结果已保存: {analysis_path}")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")


if __name__ == "__main__":
    main()
