修订标识,子系统,一级模块,二级模块,三级模块,功能用户,功能用户需求,触发事件,功能过程,子过程描述,数据移动类型,数据组,数据属性,CFP,CFP核定,评定依据
1,智慧社区,移动端,资质与服务审批中心,FR-ASC-APP-001: 动态资质认证入口,申请方,希望在App中能根据自己的认证状态，看到不同的入口引导。,用户访问“我的”页面,1. 动态加载认证入口,移动端前端App在渲染“我的”页面时，向后端请求当前用户的资质认证状态。,E,移动端资质审批_入口_认证状态请求,用户ID,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端根据用户ID，查询其最新的资质申请单状态。,R,移动端资质审批_入口_认证状态数据,申请单状态 (未申请/审核中/已通过/已驳回),1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端将认证状态返回给前端App。,X,移动端资质审批_入口_认证状态响应,认证状态,1,,
1,智慧社区,移动端,资质与服务审批中心,FR-ASC-APP-002: 个人/机构认证申请,申请方,希望能根据申请类型，填写对应的申请表单。,用户在申请入口选择申请类型,2. 加载动态申请表单,移动端用户选择主体（个人/机构）和具体的申请类型（如“餐饮服务”）。,E,移动端资质审批_申请_加载表单请求,"申请主体类型, 申请服务类型",1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端服务根据申请类型，读取预设的、与该类型关联的表单字段定义。,R,移动端资质审批_申请_表单定义,"字段列表, 字段类型, 是否必填, 校验规则",1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端将表单定义返回给前端App。,X,移动端资质审批_申请_表单定义响应,表单结构JSON,1,,
1,智慧社区,移动端,资质与服务审批中心,,申请方,希望能提交完整的申请资料以供审核。,点击“提交申请”按钮,3. 提交资质申请,移动端用户在前端填写所有表单字段。,E,移动端资质审批_申请_提交表单数据,包含所有文本字段的结构化数据,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端用户上传所有必需的证件图片（如营业执照、许可证）。,E,移动端资质审批_申请_提交证件附件,证件图片文件列表,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端服务对接收的文本数据进行业务规则验证（如社会信用代码格式）。,R,移动端资质审批_申请_业务校验规则,校验规则参数,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端服务将上传的证件图片写入对象存储(OSS)，并获取其URL。,W,移动端资质审批_申请_证件文件存储,证件图片文件,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端服务将包含所有文本数据和证件图片URL的完整申请信息，作为一条记录写入资质申请单主表，初始状态为“待预审”。,W,移动端资质审批_申请_申请单主数据,"申请单ID, 申请人ID, 申请类型, 状态(待预审), 提交的结构化数据JSON, 创建时间",1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端(日志) 系统为“接收资质申请”这一操作，写入一条详细的操作日志。,W,移动端资质审批_申请_审计日志,"用户ID, 时间, IP, 操作类型(Create), 申请单ID",1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端系统向申请人返回“接收成功，请等待审核”的提示。,X,移动端资质审批_申请_提交结果,"成功消息, 申请单ID",1,,
1,智慧社区,移动端,资质与服务审批中心,FR-ASC-APP-003: 申请状态追踪,申请方,希望能随时了解申请的审批进度和历史意见。,用户访问“我的认证”页面,4. 查看申请状态与历史,移动端前端App请求当前用户的最新资质申请单状态。,E,移动端资质审批_追踪_状态请求,用户ID,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端根据用户ID，查询其最新的资质申请单信息。,R,移动端资质审批_追踪_申请单当前状态,"申请单ID, 当前状态, 当前审批步骤名称",1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端根据申请单ID，查询其所有的历史审批记录。,R,移动端资质审批_追踪_审批历史记录,"审批步骤名称, 操作人, 操作时间, 审批动作(通过/驳回), 审批意见",1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端将当前状态和历史记录整合后，返回给前端App。,X,移动端资质审批_追踪_状态与历史响应,"当前状态, 审批历史时间线数据",1,,
1,智慧社区,移动端,资质与服务审批中心,,申请方,希望在申请被驳回后，能修改并重新提交。,点击“修改并重新提交”,5. 重新提交已驳回的申请,移动端用户在被驳回的申请详情页选择“重新接收”。,E,移动端资质审批_重提_请求,申请单ID,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端服务读取该申请单的原始接收数据，以填充编辑表单。,R,移动端资质审批_重提_原始申请数据,申请单的结构化数据JSON,1,,
1,智慧社区,移动端,资质与服务审批中心,,,,,,移动端后端将原始数据返回给前端，供用户修改。,X,移动端资质审批_重提_编辑表单视图,填充了旧数据的表单,1,,
1,智慧社区,pc端,资质与服务审批中心,FR-ASC-WEB-001: 审批工作台,平台预审员/业务审批员,希望登录后台后，能快速了解待办任务概览。,登录后访问工作台首页,6. 加载审批工作台数据,pc端前端请求当前审批员的待办任务统计数据。,E,pc端资质审批_工作台_统计请求,审批员用户ID,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端首先根据审批员ID，查询其拥有的所有审批角色。,R,pc端资质审批_工作台_审批员角色,审批角色ID列表,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端根据这些角色ID，查询其负责的所有审批步骤。,R,pc端资质审批_工作台_审批步骤,审批步骤ID列表,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端根据这些步骤ID，统计当前状态为“待审核”且current_step_id匹配的申请单数量。,R,pc端资质审批_工作台_待办数量,待我审批的数量,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端查询该审批员本月已处理的申请单数量。,R,pc端资质审批_工作台_已办数量,本月已处理的数量,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端查询最紧急的N条待办任务的简要信息。,R,pc端资质审批_工作台_紧急任务列表,"申请单ID, 申请人, 申请类型, 提交时间",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端将所有统计数据和任务列表返回给前端。,X,pc端资质审批_工作台_视图数据,"待我审批数, 本月已办数, 紧急任务列表",1,,
1,智慧社区,pc端,资质与服务审批中心,FR-ASC-WEB-002: 审批详情与操作,平台预审员/业务审批员,希望能审核一个具体的资质申请。,在待办列表点击一个申请单,7. 查看审批详情,pc端用户选择一个待办申请单。,E,pc端资质审批_详情_加载请求,申请单ID,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端根据申请单ID，读取其接收的完整结构化数据。,R,pc端资质审批_详情_申请单提交数据,申请表单的结构化数据JSON,1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端后端将申请单数据返回给前端，进行分类展示。,X,pc端资质审批_详情_视图,"申请数据, 证件图片URL",1,,
1,智慧社区,pc端,资质与服务审批中心,,平台预审员/业务审批员,,提交审批意见,8. 执行审批操作,pc端审批员在操作区选择“通过”或“驳回”，并填写审批备注。,E,pc端资质审批_操作_审批意见表单,"申请单ID, 审批动作, 审批备注",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端(安全) 后端校验当前审批员是否有权限操作此申请单的当前步骤。,R,pc端资质审批_操作_权限校验,"申请单当前步骤的审批角色ID, 当前审批员拥有的角色ID",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端(日志) 系统为本次审批操作创建一条详细的审批历史记录。,W,pc端资质审批_操作_审批历史记录,"申请单ID, 步骤名, 操作人, 动作, 备注, 时间",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端(核心逻辑) 后端读取该申请单关联的审批流程定义，以确定下一步骤。,R,pc端资质审批_操作_审批流程定义,"审批步骤列表, 步骤顺序",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端若审批通过且存在下一步，系统更新申请单的状态和当前步骤ID。,W,pc端资质审批_操作_流转后申请单状态,"状态(待下一级审核), 当前步骤ID(更新)",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端若审批通过且为最后一步，系统更新申请单状态为“已通过”。,W,pc端资质审批_操作_终审通过后申请单状态,状态(已通过),1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端若审批驳回，系统更新申请单状态为“已驳回”。,W,pc端资质审批_操作_驳回后申请单状态,状态(已驳回),1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端若审批通过且为最后一步，系统发布一个“资质审批通过”的领域事件到消息队列(MQ)，供其他模块消费。,X,pc端资质审批_操作_领域事件,"事件类型, 申请单ID, 申请人ID, 申请类型",1,,
1,智慧社区,pc端,资质与服务审批中心,,,,,,pc端系统向申请人App推送审批结果通知。,X,pc端资质审批_操作_结果通知,"申请单ID, 审批结果, 驳回原因(若有)",1,,
