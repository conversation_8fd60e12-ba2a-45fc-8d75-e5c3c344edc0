#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国移动和包支付集成系统 COSMIC功能点生成器
"""

import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class CMCCWalletGenerator(BaseCosmicGenerator):
    """中国移动和包支付集成系统COSMIC功能点生成器"""
    
    def __init__(self):
        super().__init__(
            subsystem="智慧社区",
            module_level2="支付管理", 
            module_level3="和包支付集成"
        )
    
    def generate_functional_processes(self):
        """生成和包支付集成的所有功能过程"""
        
        platforms = ["移动端", "PC端"]
        
        for platform in platforms:
            # 1. 用户发起和包支付
            self.add_functional_process(
                platform=platform,
                functional_user="用户",
                user_requirement="用户使用和包支付",
                trigger_event="用户选择和包支付方式",
                process_name=f"{platform}创建和包支付订单",
                subprocesses=[
                    {
                        'description': '接收和包支付请求',
                        'move_type': 'E',
                        'data_group': '和包支付请求',
                        'data_attributes': '用户ID,订单金额,商品标题,手机号,支付方式',
                        'context': '和包支付请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户支付权限',
                        'move_type': 'R',
                        'data_group': '用户支付权限',
                        'data_attributes': '用户ID,手机号,支付限额,账户状态,认证等级',
                        'context': '用户支付权限',
                        'cfp': 1
                    },
                    {
                        'description': '获取商户支付设置',
                        'move_type': 'R',
                        'data_group': '商户支付设置',
                        'data_attributes': '商户号,应用ID,支付密钥,API地址,环境设置',
                        'context': '商户支付设置',
                        'cfp': 1
                    },
                    {
                        'description': '存储支付订单',
                        'move_type': 'W',
                        'data_group': '支付订单',
                        'data_attributes': '订单号,用户ID,支付金额,订单状态,创建时间',
                        'context': '支付订单',
                        'cfp': 1
                    },
                    {
                        'description': '传输和包支付链接',
                        'move_type': 'X',
                        'data_group': '和包支付链接',
                        'data_attributes': '支付链接,二维码,订单号,过期时间',
                        'context': '和包支付链接',
                        'cfp': 1
                    }
                ]
            )
            
            # 2. 和包支付状态查询
            self.add_functional_process(
                platform=platform,
                functional_user="系统",
                user_requirement="查询和包支付状态",
                trigger_event="定时任务或用户主动查询",
                process_name=f"{platform}检索和包支付状态",
                subprocesses=[
                    {
                        'description': '接收支付状态检索请求',
                        'move_type': 'E',
                        'data_group': '支付状态检索请求',
                        'data_attributes': '订单号,检索类型,请求时间',
                        'context': '支付状态检索请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取本地订单状态',
                        'move_type': 'R',
                        'data_group': '本地订单状态',
                        'data_attributes': '订单号,当前状态,支付金额,创建时间',
                        'context': '本地订单状态',
                        'cfp': 1
                    },
                    {
                        'description': '传输支付状态输出',
                        'move_type': 'X',
                        'data_group': '支付状态输出',
                        'data_attributes': '订单号,支付状态,支付时间,交易流水号',
                        'context': '支付状态输出',
                        'cfp': 1
                    }
                ]
            )
            
            # 3. 和包支付结果通知处理
            self.add_functional_process(
                platform=platform,
                functional_user="和包支付平台",
                user_requirement="和包支付结果通知",
                trigger_event="和包支付平台发送异步通知",
                process_name=f"{platform}处理和包支付通知",
                subprocesses=[
                    {
                        'description': '接收和包支付通知',
                        'move_type': 'E',
                        'data_group': '和包支付通知',
                        'data_attributes': '订单号,支付状态,交易流水号,支付金额,通知时间,签名',
                        'context': '和包支付通知',
                        'cfp': 1
                    },
                    {
                        'description': '获取订单验证明细',
                        'move_type': 'R',
                        'data_group': '订单验证明细',
                        'data_attributes': '订单号,原始金额,用户ID,订单状态',
                        'context': '订单验证明细',
                        'cfp': 1
                    },
                    {
                        'description': '修改订单支付状态',
                        'move_type': 'W',
                        'data_group': '订单支付状态',
                        'data_attributes': '订单号,新状态,支付时间,交易流水号',
                        'context': '订单支付状态',
                        'cfp': 1
                    },
                    {
                        'description': '存储支付通知日志',
                        'move_type': 'W',
                        'data_group': '支付通知日志',
                        'data_attributes': '通知ID,订单号,通知内容,处理状态,处理时间',
                        'context': '支付通知日志',
                        'cfp': 1
                    },
                    {
                        'description': '传输业务处理输出',
                        'move_type': 'X',
                        'data_group': '业务处理输出',
                        'data_attributes': '处理状态,订单号,业务输出,响应时间',
                        'context': '业务处理输出',
                        'cfp': 1
                    }
                ]
            )
            
            # 4. 和包支付退款处理
            self.add_functional_process(
                platform=platform,
                functional_user="管理员",
                user_requirement="处理和包支付退款",
                trigger_event="管理员发起退款申请",
                process_name=f"{platform}执行和包支付退款",
                subprocesses=[
                    {
                        'description': '接收退款申请请求',
                        'move_type': 'E',
                        'data_group': '退款申请请求',
                        'data_attributes': '原订单号,退款金额,退款原因,申请人,申请时间',
                        'context': '退款申请请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取原订单支付记录',
                        'move_type': 'R',
                        'data_group': '原订单支付记录',
                        'data_attributes': '订单号,支付金额,支付状态,交易流水号,支付时间',
                        'context': '原订单支付记录',
                        'cfp': 1
                    },
                    {
                        'description': '存储退款订单记录',
                        'move_type': 'W',
                        'data_group': '退款订单记录',
                        'data_attributes': '退款单号,原订单号,退款金额,退款状态,创建时间',
                        'context': '退款订单记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输退款处理输出',
                        'move_type': 'X',
                        'data_group': '退款处理输出',
                        'data_attributes': '退款单号,处理状态,退款时间,失败原因',
                        'context': '退款处理输出',
                        'cfp': 1
                    }
                ]
            )
            
            # 5. 管理员查询支付记录
            self.add_functional_process(
                platform=platform,
                functional_user="管理员",
                user_requirement="查询和包支付记录",
                trigger_event="管理员发起支付记录查询",
                process_name=f"{platform}检索和包支付记录",
                subprocesses=[
                    {
                        'description': '接收支付记录检索请求',
                        'move_type': 'E',
                        'data_group': '支付记录检索请求',
                        'data_attributes': '检索条件,时间范围,用户ID,订单状态,分页参数',
                        'context': '支付记录检索请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取支付订单记录',
                        'move_type': 'R',
                        'data_group': '支付订单记录',
                        'data_attributes': '订单号,用户ID,支付金额,支付状态,支付时间,商品标题',
                        'context': '支付订单记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输支付记录集合',
                        'move_type': 'X',
                        'data_group': '支付记录集合',
                        'data_attributes': '记录总数,支付记录明细,统计汇总,分页数据',
                        'context': '支付记录集合',
                        'cfp': 1
                    }
                ]
            )
            
            # 6. 管理员配置和包支付参数
            self.add_functional_process(
                platform=platform,
                functional_user="管理员",
                user_requirement="配置和包支付参数",
                trigger_event="管理员修改支付参数",
                process_name=f"{platform}更新和包支付参数",
                subprocesses=[
                    {
                        'description': '接收支付参数更新请求',
                        'move_type': 'E',
                        'data_group': '支付参数更新请求',
                        'data_attributes': '商户号,应用ID,支付密钥,API地址,环境设置',
                        'context': '支付参数更新请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取当前支付参数',
                        'move_type': 'R',
                        'data_group': '当前支付参数',
                        'data_attributes': '商户号,应用ID,当前密钥,当前API地址,当前环境',
                        'context': '当前支付参数',
                        'cfp': 1
                    },
                    {
                        'description': '存储更新后支付参数',
                        'move_type': 'W',
                        'data_group': '更新后支付参数',
                        'data_attributes': '商户号,新应用ID,新支付密钥,新API地址,更新时间,操作人员',
                        'context': '更新后支付参数',
                        'cfp': 1
                    },
                    {
                        'description': '传输参数更新输出',
                        'move_type': 'X',
                        'data_group': '参数更新输出',
                        'data_attributes': '更新状态,更新时间,参数明细,验证输出',
                        'context': '参数更新输出',
                        'cfp': 1
                    }
                ]
            )
