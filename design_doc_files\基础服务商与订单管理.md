## 【V5.0 重构】** **2.4. 基础服务商与订单管理 (SPO - Service Provider & Order) 

#### **SPO-001: 服务商档案与社区入驻管理**

- **功能ID**: SPO-001
- **阶段**: [一期]
- **用户角色**: 社区管理员, 服务商
- **功能描述**: 管理服务商的中心化档案，并支持其申请入驻和管理在不同社区的服务资格。
- **功能要求**:
    1. **中心化档案**: 服务商在平台只拥有一个唯一的档案，记录其名称、Logo、法人信息、统一社会信用代码等核心资质信息。此档案的创建和修改需通过“资质与服务审批中心”进行。
    2. **社区入驻申请**:
        - 服务商可在其后台选择目标社区，并发起“入驻申请”。
        - 申请时可为该社区单独配置联系方式、服务范围、收费模式（见下文）等。
    3. **入驻状态管理**: 平台管理员或社区管理员可审核服务商的入驻申请。一个服务商在不同社区可以有不同的入驻状态（审核中、已入驻、已冻结）。
    4. **多社区视图**: 服务商登录后台后，可以清晰地看到自己已入驻的所有社区列表，并能切换查看在每个社区的订单、收益等情况。

#### **SPO-002: 服务目录与收费模式管理**

- **功能ID**: SPO-002
- **阶段**: [一期]
- **用户角色**: 社区管理员, 服务商
- **功能描述**: 根据服务商的业务特性，配置不同的收费模式。
- **功能要求**:
    1. **服务目录**: 服务商可在其后台管理自己的服务/商品目录。
    2. **收费模式配置**: 在申请入驻某个社区时，服务商需根据平台规则选择或被指定收费模式：
        - **模式A：订单佣金模式**: 适用于可在平台生成明确金额订单的服务（如订餐、订水）。需配置佣金比例（如5%），此数据将作为“收益中心”和“分润中心”的计算依据。
        - **模式B：引流信息费模式**: 适用于无法在线确定金额的服务（如维修、家政）。需配置单次有效引流（如“一键呼叫”点击）的费用（如3元/次）。
        - **模式C：固定服务费模式**: 可配置按月度、季度、年度收取的固定平台服务费。

#### **SPO-003: 基础订单与引流记录系统**


- **功能ID**: SPO-003
- **阶段**: [一期]
- **功能描述**: 实现服务的在线下单和订单流转，并记录所有可追踪的商业行为。
- **业务流程**:
    1. **服务发现**: 居民在App内可看到其所在社区已入驻的服务商列表。
    2. **下单/引流**:
        - 若为“订单佣金”模式的服务，用户可在线下单（可不支付），平台生成包含商品明细和总金额的订单。
        - 若为“引流信息费”模式的服务，用户点击“一键呼叫”，平台记录一次引流事件，包含用户、服务商、社区和时间。
    3. **订单/事件推送**: 系统将订单或引流事件通知推送给对应的服务商。
    4. **数据采集**: 所有生成的订单金额、引流事件，都将被自动推送至**平台收益中心**进行记录和后续处理。