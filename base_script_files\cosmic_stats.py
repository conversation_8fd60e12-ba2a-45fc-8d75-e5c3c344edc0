#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能点统计脚本
"""

import pandas as pd

def generate_cosmic_stats(file_path):
    """生成COSMIC功能点统计"""
    
    # 读取文件
    df = pd.read_csv(file_path, encoding='utf-8')
    
    print('=== 第三方商城积分消费集成系统 COSMIC功能点统计 ===')
    print(f'总功能点数: {len(df)} 个')
    print(f'总CFP值: {df["CFP"].sum()} CFP')
    print()
    
    # 处理合并单元格：向下填充功能过程名称
    df_filled = df.copy()
    df_filled['功能过程'] = df_filled['功能过程'].ffill()
    
    # 按功能过程统计
    process_stats = df_filled.groupby('功能过程').agg({
        'CFP': 'sum',
        '子过程描述': 'count'
    }).rename(columns={'子过程描述': '子过程数量'})
    
    print('=== 按功能过程统计 ===')
    for process, stats in process_stats.iterrows():
        print(f'{process}: {stats["子过程数量"]}个子过程, {stats["CFP"]}CFP')
    
    print()
    
    # 按数据移动类型统计
    move_type_stats = df['数据移动类型'].value_counts()
    print('=== 按数据移动类型统计 ===')
    for move_type, count in move_type_stats.items():
        print(f'{move_type}: {count}个')
    
    print()
    
    # 按平台统计
    platform_stats = df['一级模块'].value_counts()
    print('=== 按平台统计 ===')
    for platform, count in platform_stats.items():
        print(f'{platform}: {count}个功能点')
    
    print()
    
    # 按功能用户统计
    user_stats = df['功能用户'].value_counts()
    print('=== 按功能用户统计 ===')
    for user, count in user_stats.items():
        print(f'{user}: {count}个功能点')

if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        file_path = "cosmic_points.csv"

    generate_cosmic_stats(file_path)
