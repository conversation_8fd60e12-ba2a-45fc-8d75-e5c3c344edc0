#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能点检查工具
根据cosmic_rule.md中的规则对COSMIC文档进行检查
"""

import pandas as pd
import argparse
import sys
import re
import json
from typing import List, Dict, Tuple
from difflib import SequenceMatcher


class CosmicChecker:
    """COSMIC功能点检查器"""

    def __init__(self, verbose=False):
        self.verbose = verbose
        self.errors = []
        self.warnings = []

        # 非COSMIC类功能点关键词
        self.non_cosmic_keywords = [
            '调优', '优化', '配置', '调研', '测试', '集成', '部署', 'UI', 'UE',
            '支撑', '报告', '割接', '合法性校验', '规则校验', '输入校验',
            '联调', '设计', '现场', '实施'
        ]

        # 可能未跨层交互的关键词
        self.non_cross_layer_keywords = [
            '验证', '计算', '解码', '解析', '组装', '校验', '拼接',
            '是否', '判断', '如果', '点击', '提交'
        ]

        # 禁用的子过程描述关键词
        self.forbidden_subprocess_keywords = ['判断', '如果', '点击', '提交']

        # 禁用的数据组关键词
        self.forbidden_datagroup_keywords = ['程序']

        # 禁用的数据属性关键词
        self.forbidden_dataattr_keywords = ['数据库', '信息', '配置']

    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        if self.verbose or level in ["ERROR", "WARNING"]:
            print(f"[{level}] {message}")

    def check_file(self, file_path: str) -> Dict:
        """检查COSMIC文件"""
        self.log(f"开始检查文件: {file_path}")

        try:
            # 重置错误和警告列表
            self.errors = []
            self.warnings = []

            # 尝试不同编码读取文件
            df = self._load_file(file_path)
            if df is None:
                return {"error": "无法读取文件"}

            # 标准化列名
            df = self._standardize_columns(df)

            # 执行各项检查
            self._check_basic_rules(df)
            self._check_process_rules(df)
            self._check_duplication_rules(df)
            self._check_content_rules(df)
            self._check_cfp_rules(df)

            # 生成检查结果
            result = self._generate_result(df)

            self.log(f"检查完成，发现 {len(self.errors)} 个错误，{len(self.warnings)} 个警告")

            return result

        except Exception as e:
            error_msg = f"检查文件时出错: {str(e)}"
            self.log(error_msg, "ERROR")
            return {"error": error_msg}

    def _load_file(self, file_path: str) -> pd.DataFrame:
        """加载文件，尝试不同编码"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'iso-8859-1']

        for encoding in encodings:
            try:
                if file_path.endswith('.csv'):
                    df = pd.read_csv(file_path, encoding=encoding)
                elif file_path.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(file_path)
                else:
                    raise ValueError("不支持的文件格式")

                self.log(f"使用 {encoding} 编码成功读取文件")
                return df

            except Exception as e:
                self.log(f"使用 {encoding} 编码读取失败: {e}")
                continue

        return None

    def _standardize_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化列名"""
        expected_columns = [
            '修订标识', '子系统', '一级模块', '二级模块', '三级模块',
            '功能用户', '功能用户需求', '触发事件', '功能过程',
            '子过程描述', '数据移动类型', '数据组', '数据属性', 'CFP', 'CFP核定', '评定依据'
        ]

        # 如果列数匹配，重命名列
        if len(df.columns) == len(expected_columns):
            df.columns = expected_columns
            self.log("已标准化列名")
        else:
            self.log(f"列数不匹配，期望 {len(expected_columns)} 列，实际 {len(df.columns)} 列", "WARNING")

        return df

    def _check_basic_rules(self, df: pd.DataFrame):
        """检查基本规则"""
        self.log("检查基本规则...")

        required_columns = ['子过程描述', '数据移动类型', '数据组', '数据属性', 'CFP']

        for col in required_columns:
            if col not in df.columns:
                self.errors.append(f"缺少必要列: {col}")
                continue

            # 检查空值（功能过程列允许空值，因为是合并单元格）
            null_count = df[col].isnull().sum()
            if null_count > 0:
                self.errors.append(f"问题（三）：{col} 列存在 {null_count} 个空值")

    def _check_process_rules(self, df: pd.DataFrame):
        """检查功能过程规则"""
        self.log("检查功能过程规则...")

        if '功能过程' not in df.columns or '数据移动类型' not in df.columns:
            return

        # 处理合并单元格：向下填充功能过程名称
        df_filled = df.copy()
        df_filled['功能过程'] = df_filled['功能过程'].ffill()

        # 按功能过程分组检查
        for process_name, group in df_filled.groupby('功能过程'):
            if pd.isna(process_name):
                continue

            # 检查第一个子过程是否为E
            first_row = group.iloc[0]
            if first_row['数据移动类型'] != 'E':
                self.errors.append(f"问题（一）：功能过程 '{process_name}' 第一个子过程数据移动类型不是E")

            # 检查是否包含必要的数据移动类型（EW或EX）
            move_types = set(group['数据移动类型'].dropna())
            has_e = 'E' in move_types
            has_w_or_x = 'W' in move_types or 'X' in move_types

            if not (has_e and has_w_or_x):
                self.errors.append(f"问题（二）：功能过程 '{process_name}' 未包含必要的数据移动类型（EW或EX）")

            # 检查子过程数量
            subprocess_count = len(group)
            if subprocess_count < 3:
                self.warnings.append(f"功能过程 '{process_name}' 子过程数量过少（{subprocess_count}个），建议至少3个")
            elif subprocess_count > 8:
                self.warnings.append(f"功能过程 '{process_name}' 子过程数量过多（{subprocess_count}个），建议不超过8个")

    def _check_duplication_rules(self, df: pd.DataFrame):
        """检查重复性规则"""
        self.log("检查重复性规则...")

        # 检查功能过程重复
        if '功能过程' in df.columns:
            duplicated_processes = df[df['功能过程'].duplicated(keep=False)]
            if not duplicated_processes.empty:
                for process in duplicated_processes['功能过程'].unique():
                    if pd.notna(process):
                        self.errors.append(f"问题（四）：功能过程 '{process}' 存在重复行")

        # 检查子过程描述重复
        if '子过程描述' in df.columns:
            duplicated_subprocess = df[df['子过程描述'].duplicated(keep=False)]
            if not duplicated_subprocess.empty:
                for subprocess in duplicated_subprocess['子过程描述'].unique():
                    if pd.notna(subprocess):
                        self.errors.append(f"问题（五）：子过程描述 '{subprocess}' 存在重复行")

        # 检查数据移动类型、数据组、数据属性组合重复
        if all(col in df.columns for col in ['数据移动类型', '数据组', '数据属性']):
            combination_cols = ['数据移动类型', '数据组', '数据属性']
            duplicated_combination = df[df[combination_cols].duplicated(keep=False)]
            if not duplicated_combination.empty:
                self.errors.append(f"问题（六）：存在 {len(duplicated_combination)} 行数据移动类型、数据组、数据属性组合重复")

        # 检查数据移动类型、数据组组合重复
        if all(col in df.columns for col in ['数据移动类型', '数据组']):
            combination_cols = ['数据移动类型', '数据组']
            duplicated_combination = df[df[combination_cols].duplicated(keep=False)]
            if not duplicated_combination.empty:
                self.errors.append(f"问题（七）：存在 {len(duplicated_combination)} 行数据移动类型、数据组组合重复")

        # 检查数据移动类型、数据属性组合重复
        if all(col in df.columns for col in ['数据移动类型', '数据属性']):
            combination_cols = ['数据移动类型', '数据属性']
            duplicated_combination = df[df[combination_cols].duplicated(keep=False)]
            if not duplicated_combination.empty:
                self.errors.append(f"问题（八）：存在 {len(duplicated_combination)} 行数据移动类型、数据属性组合重复")

    def _check_content_rules(self, df: pd.DataFrame):
        """检查内容规则"""
        self.log("检查内容规则...")

        # 检查子过程描述中的禁用关键词
        if '子过程描述' in df.columns:
            for idx, subprocess_desc in df['子过程描述'].items():
                if pd.isna(subprocess_desc):
                    continue

                subprocess_desc = str(subprocess_desc)
                row_num = idx + 1 if isinstance(idx, int) else 1

                # 检查禁用关键词
                for keyword in self.forbidden_subprocess_keywords:
                    if keyword in subprocess_desc:
                        self.errors.append(f"子过程描述第 {row_num} 行包含禁用关键词 '{keyword}': {subprocess_desc}")

                # 检查可能未跨层交互的关键词
                for keyword in self.non_cross_layer_keywords:
                    if keyword in subprocess_desc and '输出' not in subprocess_desc and '保存' not in subprocess_desc:
                        self.warnings.append(f"待确认问题（十一）：子过程描述第 {row_num} 行可能未跨层交互: {subprocess_desc}")

                # 检查非COSMIC类功能点关键词
                for keyword in self.non_cosmic_keywords:
                    if keyword in subprocess_desc:
                        self.warnings.append(f"待确认问题（十二）：子过程描述第 {row_num} 行包含非COSMIC关键词 '{keyword}': {subprocess_desc}")

        # 检查数据组中的禁用关键词
        if '数据组' in df.columns:
            for idx, data_group in df['数据组'].items():
                if pd.isna(data_group):
                    continue

                data_group = str(data_group)
                row_num = idx + 1 if isinstance(idx, int) else 1
                for keyword in self.forbidden_datagroup_keywords:
                    if keyword in data_group:
                        self.errors.append(f"数据组第 {row_num} 行包含禁用关键词 '{keyword}': {data_group}")

        # 检查数据属性中的禁用关键词
        if '数据属性' in df.columns:
            for idx, data_attr in df['数据属性'].items():
                if pd.isna(data_attr):
                    continue

                data_attr = str(data_attr)
                row_num = idx + 1 if isinstance(idx, int) else 1
                for keyword in self.forbidden_dataattr_keywords:
                    if keyword in data_attr:
                        self.errors.append(f"数据属性第 {row_num} 行包含禁用关键词 '{keyword}': {data_attr}")

        # 检查子过程描述相似度
        if '子过程描述' in df.columns:
            self._check_similarity(df['子过程描述'])

    def _check_similarity(self, subprocess_series: pd.Series):
        """检查子过程描述相似度"""
        subprocess_list = subprocess_series.dropna().tolist()

        for i in range(len(subprocess_list)):
            for j in range(i + 1, len(subprocess_list)):
                similarity = SequenceMatcher(None, str(subprocess_list[i]), str(subprocess_list[j])).ratio()
                if similarity > 0.85:
                    self.warnings.append(f"待确认问题（十）：子过程描述相似度过高（{similarity:.2%}）: '{subprocess_list[i]}' 与 '{subprocess_list[j]}'")

    def _check_cfp_rules(self, df: pd.DataFrame):
        """检查CFP规则"""
        self.log("检查CFP规则...")

        if 'CFP' not in df.columns:
            return

        valid_cfp_values = [1, 0.33, 0]

        for idx, cfp_value in df['CFP'].items():
            if pd.isna(cfp_value):
                continue

            row_num = idx + 1 if isinstance(idx, int) else 1
            try:
                cfp_float = float(cfp_value)
                if cfp_float not in valid_cfp_values:
                    self.errors.append(f"问题（九）：第 {row_num} 行CFP值 {cfp_float} 不为1、0.33或0")
            except (ValueError, TypeError):
                self.errors.append(f"问题（九）：第 {row_num} 行CFP值 '{cfp_value}' 不是有效数字")

    def _generate_result(self, df: pd.DataFrame) -> Dict:
        """生成检查结果"""
        total_cfp = 0
        if 'CFP' in df.columns:
            try:
                total_cfp = df['CFP'].astype(float).sum()
            except:
                total_cfp = 0

        result = {
            "file_info": {
                "total_rows": len(df),
                "total_cfp": total_cfp,
                "columns": df.columns.tolist()
            },
            "check_result": {
                "errors": self.errors,
                "warnings": self.warnings,
                "error_count": len(self.errors),
                "warning_count": len(self.warnings)
            },
            "status": "success" if len(self.errors) == 0 else "failed"
        }

        return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="COSMIC功能点检查工具")
    parser.add_argument("--file", "-f", required=True, help="要检查的COSMIC文件路径")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    parser.add_argument("--output", "-o", help="输出结果文件路径（JSON格式）")

    args = parser.parse_args()

    # 创建检查器
    checker = CosmicChecker(verbose=args.verbose)

    # 执行检查
    result = checker.check_file(args.file)

    # 输出结果
    if "error" in result:
        print(f"❌ 检查失败: {result['error']}")
        sys.exit(1)

    print("\n" + "="*60)
    print("COSMIC功能点检查结果")
    print("="*60)

    file_info = result["file_info"]
    check_result = result["check_result"]

    print(f"📊 文件信息:")
    print(f"  总行数: {file_info['total_rows']}")
    print(f"  总CFP: {file_info['total_cfp']}")

    print(f"\n🔍 检查结果:")
    print(f"  错误数量: {check_result['error_count']}")
    print(f"  警告数量: {check_result['warning_count']}")

    if check_result['errors']:
        print(f"\n❌ 错误列表:")
        for i, error in enumerate(check_result['errors'], 1):
            print(f"  {i}. {error}")

    if check_result['warnings']:
        print(f"\n⚠️ 警告列表:")
        for i, warning in enumerate(check_result['warnings'], 1):
            print(f"  {i}. {warning}")

    if check_result['error_count'] == 0 and check_result['warning_count'] == 0:
        print("\n✅ 检查通过，未发现问题！")

    # 保存结果
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n💾 结果已保存到: {args.output}")


if __name__ == "__main__":
    main()