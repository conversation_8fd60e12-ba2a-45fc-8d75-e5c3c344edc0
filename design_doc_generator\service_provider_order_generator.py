#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础服务商与订单管理 COSMIC功能点生成器
基于设计文档生成符合COSMIC规范的功能点
"""

import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class ServiceProviderOrderGenerator(BaseCosmicGenerator):
    """基础服务商与订单管理COSMIC功能点生成器"""
    
    def __init__(self):
        super().__init__(
            subsystem="智慧社区",
            module_level2="基础服务商与订单管理", 
            module_level3="服务商档案与社区入驻管理"
        )
    
    def generate_functional_processes(self):
        """生成基础服务商与订单管理的所有功能过程"""
        
        platforms = ["PC端", "移动端"]
        
        for platform in platforms:
            # SPO-001: 服务商档案与社区入驻管理
            # 1. 服务商档案管理
            self.add_functional_process(
                platform=platform,
                functional_user="服务商",
                user_requirement="管理服务商的中心化档案信息",
                trigger_event="服务商登录后台访问档案管理",
                process_name=f"{platform}管理服务商档案",
                subprocesses=[
                    {
                        'description': '接收档案管理请求',
                        'move_type': 'E',
                        'data_group': '档案管理请求',
                        'data_attributes': '服务商ID,操作类型,请求时间',
                        'context': '档案管理请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务商档案数据',
                        'move_type': 'R',
                        'data_group': '服务商档案数据',
                        'data_attributes': '服务商名称,Logo,法人信息,统一社会信用代码,核心资质信息',
                        'context': '服务商档案数据',
                        'cfp': 1
                    },
                    {
                        'description': '传输档案管理视图',
                        'move_type': 'X',
                        'data_group': '档案管理视图',
                        'data_attributes': '档案详情,编辑权限,操作按钮',
                        'context': '档案管理视图',
                        'cfp': 1
                    }
                ]
            )
            
            # 2. 社区入驻申请
            self.add_functional_process(
                platform=platform,
                functional_user="服务商",
                user_requirement="申请入驻目标社区",
                trigger_event="服务商选择社区并提交入驻申请",
                process_name=f"{platform}提交社区入驻申请",
                subprocesses=[
                    {
                        'description': '接收入驻申请请求',
                        'move_type': 'E',
                        'data_group': '入驻申请请求',
                        'data_attributes': '服务商ID,目标社区ID,联系方式,服务范围,收费模式',
                        'context': '入驻申请请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取社区入驻规则',
                        'move_type': 'R',
                        'data_group': '社区入驻规则',
                        'data_attributes': '社区ID,入驻条件,审核流程,收费模式限制',
                        'context': '社区入驻规则',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务商资质状态',
                        'move_type': 'R',
                        'data_group': '服务商资质状态',
                        'data_attributes': '资质审核状态,证照有效期,业务范围',
                        'context': '服务商资质状态',
                        'cfp': 1
                    },
                    {
                        'description': '存储入驻申请记录',
                        'move_type': 'W',
                        'data_group': '入驻申请记录',
                        'data_attributes': '申请ID,申请状态,申请时间,配置参数',
                        'context': '入驻申请记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输申请提交结果',
                        'move_type': 'X',
                        'data_group': '申请提交结果',
                        'data_attributes': '申请ID,提交状态,后续流程说明',
                        'context': '申请提交结果',
                        'cfp': 1
                    }
                ]
            )
            
            # 3. 入驻状态管理（管理员审核）
            self.add_functional_process(
                platform=platform,
                functional_user="社区管理员",
                user_requirement="审核服务商的入驻申请",
                trigger_event="管理员访问入驻申请审核页面",
                process_name=f"{platform}审核入驻申请",
                subprocesses=[
                    {
                        'description': '接收审核管理请求',
                        'move_type': 'E',
                        'data_group': '审核管理请求',
                        'data_attributes': '管理员ID,社区ID,筛选条件',
                        'context': '审核管理请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取待审核申请列表',
                        'move_type': 'R',
                        'data_group': '待审核申请列表',
                        'data_attributes': '申请ID,服务商名称,申请时间,申请状态,服务类型',
                        'context': '待审核申请列表',
                        'cfp': 1
                    },
                    {
                        'description': '传输审核管理视图',
                        'move_type': 'X',
                        'data_group': '审核管理视图',
                        'data_attributes': '申请列表,审核操作,筛选选项',
                        'context': '审核管理视图',
                        'cfp': 1
                    }
                ]
            )
            
            # 4. 执行审核操作
            self.add_functional_process(
                platform=platform,
                functional_user="社区管理员",
                user_requirement="对入驻申请进行审核决策",
                trigger_event="管理员提交审核意见",
                process_name=f"{platform}执行入驻审核",
                subprocesses=[
                    {
                        'description': '接收审核决策请求',
                        'move_type': 'E',
                        'data_group': '审核决策请求',
                        'data_attributes': '申请ID,审核结果,审核意见,管理员ID',
                        'context': '审核决策请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取申请详细数据',
                        'move_type': 'R',
                        'data_group': '申请详细数据',
                        'data_attributes': '服务商档案,申请配置,当前状态',
                        'context': '申请详细数据',
                        'cfp': 1
                    },
                    {
                        'description': '修改入驻申请状态',
                        'move_type': 'W',
                        'data_group': '入驻申请状态',
                        'data_attributes': '申请ID,新状态,审核时间,审核人',
                        'context': '入驻申请状态',
                        'cfp': 1
                    },
                    {
                        'description': '存储审核历史记录',
                        'move_type': 'W',
                        'data_group': '审核历史记录',
                        'data_attributes': '审核ID,申请ID,审核动作,审核意见,审核时间',
                        'context': '审核历史记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输审核结果通知',
                        'move_type': 'X',
                        'data_group': '审核结果通知',
                        'data_attributes': '审核结果,通知内容,后续操作指引',
                        'context': '审核结果通知',
                        'cfp': 1
                    }
                ]
            )
            
            # 5. 多社区视图管理
            self.add_functional_process(
                platform=platform,
                functional_user="服务商",
                user_requirement="查看已入驻的所有社区列表",
                trigger_event="服务商登录后台查看社区列表",
                process_name=f"{platform}查看多社区视图",
                subprocesses=[
                    {
                        'description': '接收社区视图请求',
                        'move_type': 'E',
                        'data_group': '社区视图请求',
                        'data_attributes': '服务商ID,查询条件,排序方式',
                        'context': '社区视图请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取入驻社区列表',
                        'move_type': 'R',
                        'data_group': '入驻社区列表',
                        'data_attributes': '社区ID,社区名称,入驻状态,入驻时间,收费模式',
                        'context': '入驻社区列表',
                        'cfp': 1
                    },
                    {
                        'description': '获取社区业务统计',
                        'move_type': 'R',
                        'data_group': '社区业务统计',
                        'data_attributes': '订单数量,收益金额,服务评价,活跃度',
                        'context': '社区业务统计',
                        'cfp': 1
                    },
                    {
                        'description': '传输多社区视图数据',
                        'move_type': 'X',
                        'data_group': '多社区视图数据',
                        'data_attributes': '社区列表,统计卡片,切换操作',
                        'context': '多社区视图数据',
                        'cfp': 1
                    }
                ]
            )

            # SPO-002: 服务目录与收费模式管理
            # 1. 服务目录管理
            self.add_functional_process(
                platform=platform,
                functional_user="服务商",
                user_requirement="管理自己的服务商品目录",
                trigger_event="服务商访问服务目录管理页面",
                process_name=f"{platform}管理服务目录",
                subprocesses=[
                    {
                        'description': '接收目录管理请求',
                        'move_type': 'E',
                        'data_group': '目录管理请求',
                        'data_attributes': '服务商ID,操作类型,目录分类',
                        'context': '目录管理请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务目录数据',
                        'move_type': 'R',
                        'data_group': '服务目录数据',
                        'data_attributes': '服务ID,服务名称,服务描述,价格,分类,状态',
                        'context': '服务目录数据',
                        'cfp': 1
                    },
                    {
                        'description': '传输目录管理视图',
                        'move_type': 'X',
                        'data_group': '目录管理视图',
                        'data_attributes': '服务列表,分类筛选,编辑操作',
                        'context': '目录管理视图',
                        'cfp': 1
                    }
                ]
            )

            # 2. 收费模式配置
            self.add_functional_process(
                platform=platform,
                functional_user="服务商",
                user_requirement="配置不同社区的收费模式",
                trigger_event="服务商配置社区收费模式",
                process_name=f"{platform}配置收费模式",
                subprocesses=[
                    {
                        'description': '接收收费配置请求',
                        'move_type': 'E',
                        'data_group': '收费配置请求',
                        'data_attributes': '服务商ID,社区ID,收费模式类型,配置参数',
                        'context': '收费配置请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取收费模式规则',
                        'move_type': 'R',
                        'data_group': '收费模式规则',
                        'data_attributes': '模式类型,佣金比例范围,引流费用标准,固定费用标准',
                        'context': '收费模式规则',
                        'cfp': 1
                    },
                    {
                        'description': '获取社区收费限制',
                        'move_type': 'R',
                        'data_group': '社区收费限制',
                        'data_attributes': '社区ID,允许模式,费率限制,特殊规则',
                        'context': '社区收费限制',
                        'cfp': 1
                    },
                    {
                        'description': '存储收费模式配置',
                        'move_type': 'W',
                        'data_group': '收费模式配置',
                        'data_attributes': '配置ID,服务商ID,社区ID,模式类型,费率参数,生效时间',
                        'context': '收费模式配置',
                        'cfp': 1
                    },
                    {
                        'description': '传输配置结果',
                        'move_type': 'X',
                        'data_group': '配置结果',
                        'data_attributes': '配置状态,生效时间,费率详情',
                        'context': '配置结果',
                        'cfp': 1
                    }
                ]
            )

            # SPO-003: 基础订单与引流记录系统
            # 1. 服务发现
            self.add_functional_process(
                platform=platform,
                functional_user="居民用户",
                user_requirement="查看所在社区的服务商列表",
                trigger_event="居民在App内浏览服务商",
                process_name=f"{platform}浏览社区服务商",
                subprocesses=[
                    {
                        'description': '接收服务发现请求',
                        'move_type': 'E',
                        'data_group': '服务发现请求',
                        'data_attributes': '用户ID,社区ID,服务分类,搜索关键词',
                        'context': '服务发现请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户社区信息',
                        'move_type': 'R',
                        'data_group': '用户社区信息',
                        'data_attributes': '用户ID,所属社区ID,社区名称,权限范围',
                        'context': '用户社区信息',
                        'cfp': 1
                    },
                    {
                        'description': '获取社区服务商列表',
                        'move_type': 'R',
                        'data_group': '社区服务商列表',
                        'data_attributes': '服务商ID,服务商名称,服务类型,评分,入驻状态',
                        'context': '社区服务商列表',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务商服务目录',
                        'move_type': 'R',
                        'data_group': '服务商服务目录',
                        'data_attributes': '服务ID,服务名称,价格,描述,可用状态',
                        'context': '服务商服务目录',
                        'cfp': 1
                    },
                    {
                        'description': '传输服务发现结果',
                        'move_type': 'X',
                        'data_group': '服务发现结果',
                        'data_attributes': '服务商列表,服务详情,联系方式,下单入口',
                        'context': '服务发现结果',
                        'cfp': 1
                    }
                ]
            )

            # 2. 在线下单（订单佣金模式）
            self.add_functional_process(
                platform=platform,
                functional_user="居民用户",
                user_requirement="对订单佣金模式的服务进行在线下单",
                trigger_event="用户选择服务并提交订单",
                process_name=f"{platform}创建服务订单",
                subprocesses=[
                    {
                        'description': '接收订单创建请求',
                        'move_type': 'E',
                        'data_group': '订单创建请求',
                        'data_attributes': '用户ID,服务商ID,服务项目,数量,总金额',
                        'context': '订单创建请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务商收费配置',
                        'move_type': 'R',
                        'data_group': '服务商收费配置',
                        'data_attributes': '收费模式,佣金比例,服务价格,可用状态',
                        'context': '服务商收费配置',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户订单权限',
                        'move_type': 'R',
                        'data_group': '用户订单权限',
                        'data_attributes': '用户等级,订单限额,社区权限,信用状态',
                        'context': '用户订单权限',
                        'cfp': 1
                    },
                    {
                        'description': '存储订单记录',
                        'move_type': 'W',
                        'data_group': '订单记录',
                        'data_attributes': '订单ID,订单状态,商品明细,总金额,创建时间',
                        'context': '订单记录',
                        'cfp': 1
                    },
                    {
                        'description': '存储收益计算数据',
                        'move_type': 'W',
                        'data_group': '收益计算数据',
                        'data_attributes': '订单ID,佣金金额,收益类型,社区ID,服务商ID',
                        'context': '收益计算数据',
                        'cfp': 1
                    },
                    {
                        'description': '传输订单创建结果',
                        'move_type': 'X',
                        'data_group': '订单创建结果',
                        'data_attributes': '订单ID,订单详情,支付信息,联系方式',
                        'context': '订单创建结果',
                        'cfp': 1
                    }
                ]
            )

            # 3. 引流记录（引流信息费模式）
            self.add_functional_process(
                platform=platform,
                functional_user="居民用户",
                user_requirement="对引流信息费模式的服务进行一键呼叫",
                trigger_event="用户点击一键呼叫按钮",
                process_name=f"{platform}记录引流事件",
                subprocesses=[
                    {
                        'description': '接收引流请求',
                        'move_type': 'E',
                        'data_group': '引流请求',
                        'data_attributes': '用户ID,服务商ID,服务类型,联系需求',
                        'context': '引流请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取引流费用配置',
                        'move_type': 'R',
                        'data_group': '引流费用配置',
                        'data_attributes': '单次引流费用,收费模式,服务商状态',
                        'context': '引流费用配置',
                        'cfp': 1
                    },
                    {
                        'description': '存储引流事件记录',
                        'move_type': 'W',
                        'data_group': '引流事件记录',
                        'data_attributes': '事件ID,用户ID,服务商ID,社区ID,引流时间,费用金额',
                        'context': '引流事件记录',
                        'cfp': 1
                    },
                    {
                        'description': '存储引流收益数据',
                        'move_type': 'W',
                        'data_group': '引流收益数据',
                        'data_attributes': '事件ID,引流费用,收益类型,社区归属',
                        'context': '引流收益数据',
                        'cfp': 1
                    },
                    {
                        'description': '传输联系信息',
                        'move_type': 'X',
                        'data_group': '联系信息',
                        'data_attributes': '服务商联系方式,服务说明,后续流程',
                        'context': '联系信息',
                        'cfp': 1
                    }
                ]
            )

            # 4. 订单/事件推送给服务商
            self.add_functional_process(
                platform=platform,
                functional_user="服务商",
                user_requirement="接收来自平台的订单或引流通知",
                trigger_event="系统推送订单或引流事件",
                process_name=f"{platform}接收业务通知",
                subprocesses=[
                    {
                        'description': '接收通知推送请求',
                        'move_type': 'E',
                        'data_group': '通知推送请求',
                        'data_attributes': '服务商ID,通知类型,业务数据,优先级',
                        'context': '通知推送请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务商通知配置',
                        'move_type': 'R',
                        'data_group': '服务商通知配置',
                        'data_attributes': '通知方式,接收时间,联系方式,推送权限',
                        'context': '服务商通知配置',
                        'cfp': 1
                    },
                    {
                        'description': '存储通知发送记录',
                        'move_type': 'W',
                        'data_group': '通知发送记录',
                        'data_attributes': '通知ID,发送时间,发送状态,业务关联ID',
                        'context': '通知发送记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输业务通知',
                        'move_type': 'X',
                        'data_group': '业务通知',
                        'data_attributes': '订单详情,客户联系方式,服务要求,处理时限',
                        'context': '业务通知',
                        'cfp': 1
                    }
                ]
            )

            # 5. 数据采集到收益中心
            self.add_functional_process(
                platform=platform,
                functional_user="系统",
                user_requirement="将订单和引流数据推送到平台收益中心",
                trigger_event="订单创建或引流事件发生",
                process_name=f"{platform}推送收益数据",
                subprocesses=[
                    {
                        'description': '接收收益数据推送触发',
                        'move_type': 'E',
                        'data_group': '收益数据推送触发',
                        'data_attributes': '业务ID,业务类型,收益金额,触发时间',
                        'context': '收益数据推送触发',
                        'cfp': 1
                    },
                    {
                        'description': '获取业务收益明细',
                        'move_type': 'R',
                        'data_group': '业务收益明细',
                        'data_attributes': '订单金额,佣金计算,引流费用,社区归属,服务商信息',
                        'context': '业务收益明细',
                        'cfp': 1
                    },
                    {
                        'description': '存储收益推送日志',
                        'move_type': 'W',
                        'data_group': '收益推送日志',
                        'data_attributes': '推送ID,业务ID,推送状态,推送时间,错误信息',
                        'context': '收益推送日志',
                        'cfp': 1
                    },
                    {
                        'description': '传输收益数据到收益中心',
                        'move_type': 'X',
                        'data_group': '收益数据到收益中心',
                        'data_attributes': '来源单据ID,收益类型,金额,社区ID,服务商ID,业务描述',
                        'context': '收益数据到收益中心',
                        'cfp': 1
                    }
                ]
            )
