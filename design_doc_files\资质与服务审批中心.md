## **【V5.0 新增】 7. 资质与服务审批中心 (ASC - Approval & Service Center)**

**版本**: 1.0 **模块归属**: 平台基础能力 **关联模块**: 用户管理(SYS), 服务商管理(SPO), 志愿者管理(将来)

#### **1. 模块概述**

##### **1.1. 目标**

本模块旨在建立一套**线上化、流程化、可配置**的资质审核体系。核心目标是：

1. **建立信任**: 通过引入多级、多角色（包括政府部门）的线上审批流程，为平台服务商和参与者的资质提供强有力的信用背书。
2. **保障质量**: 确保所有入驻平台的个人与机构都经过标准化的资质审核，从源头保障服务质量。
3. **提升效率**: 将传统的线下审批搬到线上，通过标准化的工作流引擎，提升审批效率，降低沟通和管理成本。

##### **1.2. 核心用户角色**

|角色|所属端|核心职责|
|---|---|---|
|**申请方**|移动App|希望入驻的服务商、机构或希望成为认证志愿者的个人。|
|**平台预审员**|Web后台|平台运营人员，负责对申请材料的完整性和基础合规性进行第一道审核。|
|**业务审批员**|Web后台|社区、街道办或政府职能部门（如卫生局）的工作人员，负责对其职权范围内的申请进行专业审批。|
|**平台超级管理员**|Web后台|系统的最高权限者，负责设计和配置审批流程、管理审批角色和账号。|

##### **1.3. 核心数据实体 (Data Models)**

- **`ApprovalRequest` (资质申请单)**:
    - `request_id` (主键)
    - `applicant_user_id` (申请人关联的平台用户ID)
    - `applicant_type` (枚举: `INDIVIDUAL`, `ORGANIZATION`)
    - `request_type` (枚举: "志愿者", "餐饮服务", "适老化改造"...)
    - `status` (枚举: `DRAFT`, `PENDING_PRE_REVIEW`, `PENDING_LEVEL_2_REVIEW`, ..., `APPROVED`, `REJECTED`)
    - `submission_data` (JSONB类型: 存储申请表单提交的全部结构化数据)
    - `current_step_id` (当前走到的审批步骤ID)
- **`ApprovalFlow` (审批流程)**:
    - `flow_id` (主键)
    - `flow_name` (流程名称, e.g., "医疗服务商入驻流程")
    - `linked_request_types` (适用的申请类型列表)
- **`ApprovalStep` (审批步骤)**:
    - `step_id` (主键)
    - `flow_id` (关联审批流程)
    - `step_order` (步骤顺序, e.g., 1, 2, 3)
    - `step_name` (步骤名称, e.g., "平台预审", "街道办审核")
    - `approver_role_id` (关联负责此步骤的审批角色ID)
- **`ApprovalHistory` (审批历史记录)**:
    - `history_id` (主键)
    - `request_id` (关联申请单)
    - `step_name` (操作时所在的步骤名称)
    - `operator_name` (操作人姓名)
    - `action` (枚举: `提交申请`, `通过`, `驳回`)
    - `comment` (审批意见/驳回原因)
    - `timestamp` (操作时间)
- **`ApproverRole` (审批角色)**:
    - `role_id` (主键)
    - `role_name` (角色名称, e.g., "平台预审员", "沙河镇街道办审批员")

---

#### **2. 详细功能规格**

##### **2.1. ASC-APP: 申请端 (移动App)**

- **FR-ASC-APP-001: 资质认证入口**
    - **用户故事**: 作为一个想在平台提供维修服务的师傅，我希望能方便地在App里找到申请入口。
    - **界面**: 在App“我的”页面，提供“**资质认证**”或“**成为服务方**”的入口。
    - **逻辑**: 入口文案和跳转目标根据用户的认证状态动态变化（未认证 -> 申请页；审核中 -> 状态页；已认证 -> 资质展示页）。
- **FR-ASC-APP-002: 个人/机构认证申请**
    - **用户故事**: 我希望能根据我的身份（个人/公司）填写不同的、清晰的申请表格，并上传所需证件。
    - **流程**:
        1. 用户选择申请主体：“个人认证” 或 “机构/商户认证”。
        2. 用户选择具体的申请类型，如“志愿者”、“餐饮服务”等。
        3. 系统根据所选类型，展示对应的申请表单。
    - **表单字段 (以机构认证为例)**:
        - 机构全称、统一社会信用代码、营业执照（图片上传）、行业许可证（根据申请类型动态显示，如餐饮需《食品经营许可证》）、法人信息、运营者信息等。
    - **交互**: 上传图片时支持拍照和从相册选择。提交前对必填项进行前端校验。
- **FR-ASC-APP-003: 申请状态追踪**
    - **用户故事**: 提交申请后，我想随时知道我的申请到哪一步了，如果被驳回，我想清楚地知道原因。
    - **界面**: 一个清晰的申请状态时间线页面。
    - **展示**:
        - **当前状态**: 醒目地显示“审核中”、“已通过”或“已驳回”。
        - **审批历史**: 调用`ApprovalHistory`数据，以时间倒序展示每一步的审批详情（审批节点、操作人、时间和意见）。
        - **驳回原因**: 如果被驳回，必须明确展示驳回意见，并提供“修改并重新提交”的入口。

##### **2.2. ASC-WEB: 审批端 (Web后台)**

- **FR-ASC-WEB-001: 审批工作台 (Dashboard)**
    - **用户故事**: 作为一名街道办审批员，我登录后台后，只想看到分派给我的任务，并了解我的工作量。
    - **界面**: 审批员登录后，进入个人工作台。
    - **组件**:
        - **待我审批**: 数字卡片，`COUNT(*)` from `ApprovalRequest` where `status` like 'PENDING_%' AND `current_step.approver_role_id` is one of my roles。
        - **本月已处理**: 类似逻辑的已完成任务计数。
        - **待办任务列表**: 一个简要列表，展示最紧急的5条待办申请。
- **FR-ASC-WEB-002: 审批详情与操作**
    - **用户故事**: 在审核一个餐饮服务商的入驻申请时，我需要能清晰地看到他提交的所有资料，并能方便地做出“通过”或“驳回”的决定。
    - **界面**:
        - **信息区**: 清晰、分类展示申请人提交的所有文本信息和证照图片（图片需支持点击放大、旋转）。
        - **操作区**:
            - **审批意见**: “通过” / “驳回” (单选框)。
            - **审批备注**: (文本域) 审批意见的说明。若选择“驳回”，此项为必填。
            - **提交按钮**: [提交审批意见]。
    - **后端核心逻辑**:
        1. 审批员提交操作后，后端**记录一条`ApprovalHistory`**。
        2. **如果“驳回”**:
            - 将`ApprovalRequest`的`status`更新为`REJECTED`。
            - 流程结束，并通知申请人。
        3. **如果“通过”**:
            - 查找当前申请关联的`ApprovalFlow`中的下一步`ApprovalStep`。
            - **若存在下一步**: 更新`ApprovalRequest`的`status`和`current_step_id`到下一步，并将任务推送给下一步的审批角色。
            - **若不存在下一步（即终审通过）**:
                - 将`ApprovalRequest`的`status`更新为`APPROVED`。
                - **触发后续业务**: 发布一个“**资质审批通过**”的领域事件(`event.qualification.approved`)。其他模块（如SPO）可以订阅此事件，自动为该用户/机构创建服务商档案、开通相应权限。
                - 通知申请人最终审批通过。

##### **2.3. ASC-CFG: 审批流配置后台 (Web后台 - 仅超级管理员)**

- **FR-ASC-CFG-001: 审批流程管理**
    - **用户故事**: 作为平台超级管理员，当我们需要新增一种“家政服务”认证时，我希望能快速配置一套新的“平台预审 -> 社区审核”的二级审批流程。
    - **界面**: 一个审批流程列表，支持对流程的增、删、改。
    - **创建/编辑流程**:
        1. **定义流程名称**: 如“家政服务商入驻审批流”。
        2. **关联申请类型**: (多选框) 勾选此流程适用于哪些申请类型（如“家政保洁”）。
        3. **配置审批步骤**:
            - 提供一个**可拖拽排序**的步骤列表构建器。
            - 点击“添加步骤”，可从`ApproverRole`列表中选择一个审批角色，形成一个审批节点。
            - 可以添加多个节点，并通过拖拽调整它们的顺序，例如：
                - 步骤1：[平台预审员]
                - 步骤2：[社区/街道办审批员]
- **FR-ASC-CFG-002: 审批角色与账号管理**
    - **用户故事**: 我们街道办新来了一位同事负责审批，我需要能为他创建一个审批账号。
    - **功能**:
        1. **管理审批角色**: 增/删/改`ApproverRole`（如“昌平区市场监督管理局”）。
        2. **管理审批员账号**:
            - 从平台用户列表中搜索用户。
            - 为用户**授予**一个或多个`ApproverRole`。
            - 可以随时解除用户的审批角色。