#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能点生成器工厂
提供统一的生成器创建和管理接口
"""

import os
import sys
import importlib.util
import inspect
from typing import Dict, Type

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class CosmicGeneratorFactory:
    """COSMIC功能点生成器工厂"""

    # 注册的生成器类型
    _generators: Dict[str, Type[BaseCosmicGenerator]] = {}
    _initialized = False
    
    @classmethod
    def _discover_generators(cls):
        """动态发现design_doc_generator目录下的所有生成器"""
        if cls._initialized:
            return

        generator_dir = os.path.dirname(os.path.abspath(__file__))

        # 遍历目录下的所有Python文件
        for filename in os.listdir(generator_dir):
            if filename.endswith('_generator.py') and filename != 'base_cosmic_generator.py':
                module_name = filename[:-3]  # 去掉.py后缀
                file_path = os.path.join(generator_dir, filename)

                try:
                    # 动态导入模块
                    spec = importlib.util.spec_from_file_location(module_name, file_path)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # 查找继承自BaseCosmicGenerator的类
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and
                            issubclass(obj, BaseCosmicGenerator) and
                            obj != BaseCosmicGenerator):

                            # 生成器名称（去掉Generator后缀并转换为snake_case）
                            generator_name = cls._class_name_to_generator_name(name)
                            cls._generators[generator_name] = obj
                            print(f"🔍 发现生成器: {generator_name} ({name})")

                except Exception as e:
                    print(f"⚠️ 加载生成器失败 {filename}: {e}")

        cls._initialized = True

    @classmethod
    def _class_name_to_generator_name(cls, class_name: str) -> str:
        """将类名转换为生成器名称"""
        # 去掉Generator后缀
        if class_name.endswith('Generator'):
            class_name = class_name[:-9]

        # 转换为snake_case
        import re
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', class_name)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    @classmethod
    def register_generator(cls, name: str, generator_class: Type[BaseCosmicGenerator]):
        """注册新的生成器类型"""
        cls._generators[name] = generator_class
    
    @classmethod
    def create_generator(cls, generator_type: str) -> BaseCosmicGenerator:
        """创建指定类型的生成器"""
        # 确保已发现所有生成器
        cls._discover_generators()

        if generator_type not in cls._generators:
            available_types = ', '.join(cls._generators.keys())
            raise ValueError(f"未知的生成器类型: {generator_type}. 可用类型: {available_types}")

        generator_class = cls._generators[generator_type]
        return generator_class()
    
    @classmethod
    def list_generators(cls) -> Dict[str, str]:
        """列出所有可用的生成器"""
        # 确保已发现所有生成器
        cls._discover_generators()

        generators_info = {}
        for name, generator_class in cls._generators.items():
            # 创建临时实例获取描述信息
            temp_instance = generator_class()
            description = f"{temp_instance.subsystem} - {temp_instance.module_level2} - {temp_instance.module_level3}"
            generators_info[name] = description
        return generators_info
    
    @classmethod
    def generate_cosmic_points(cls, generator_type: str, output_filename: str = None) -> str:
        """生成指定类型的COSMIC功能点"""

        # 创建生成器
        generator = cls.create_generator(generator_type)

        # 如果没有指定输出文件名，则自动生成
        if output_filename is None:
            # 确保cosmic_results目录存在
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            results_dir = os.path.join(parent_dir, 'cosmic_results')
            os.makedirs(results_dir, exist_ok=True)

            output_filename = os.path.join(results_dir, f"{generator_type}_cosmic_points.csv")

        # 生成功能点
        return generator.generate(output_filename)


def main():
    """命令行入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description="COSMIC功能点生成器")
    parser.add_argument("--type", "-t",
                       help="生成器类型 (third_party_mall, cmcc_wallet)")
    parser.add_argument("--output", "-o",
                       help="输出文件名 (默认自动生成)")
    parser.add_argument("--list", "-l", action="store_true",
                       help="列出所有可用的生成器类型")
    
    args = parser.parse_args()
    
    factory = CosmicGeneratorFactory()
    
    # 列出可用生成器
    if args.list:
        print("可用的COSMIC功能点生成器:")
        generators = factory.list_generators()
        for name, description in generators.items():
            print(f"  {name}: {description}")
        return

    # 检查是否提供了生成器类型
    if not args.type:
        print("❌ 错误: 必须指定生成器类型")
        print("使用 --list 参数查看可用的生成器类型")
        sys.exit(1)

    try:
        # 生成COSMIC功能点
        output_file = factory.generate_cosmic_points(args.type, args.output)
        print(f"\n✅ COSMIC功能点生成成功!")
        print(f"📄 输出文件: {output_file}")

        # 提示后续操作
        print(f"\n📋 后续操作建议:")
        print(f"1. 检查生成的功能点: python cosmic_checker.py --file {output_file}")
        print(f"2. 转换为Excel格式: python csv_to_xlsx_merger.py --input {output_file}")
        print(f"3. 生成统计报告: python cosmic_stats.py {output_file}")

    except ValueError as e:
        print(f"❌ 错误: {e}")
        print("\n使用 --list 参数查看可用的生成器类型")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
