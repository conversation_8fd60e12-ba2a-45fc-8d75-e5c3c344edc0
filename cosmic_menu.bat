@echo off
chcp 65001 >nul
title COSMIC Generator
color 0A

:menu
cls
echo.
echo ========================================
echo    COSMIC Function Point Generator
echo ========================================
echo.
echo 1. Generate COSMIC Function Points
echo 2. Check Function Points
echo 3. Convert to Excel
echo 4. Generate Statistics
echo 5. Exit
echo.
set /p choice="Select option (1-5): "

if "%choice%"=="1" goto generate
if "%choice%"=="2" goto check
if "%choice%"=="3" goto convert
if "%choice%"=="4" goto stats
if "%choice%"=="5" goto exit

echo Invalid choice!
pause
goto menu

:generate
echo.
echo Generating COSMIC Function Points...
python base_script_files\generate_cosmic.py --interactive
pause
goto menu

:check
echo.
echo Available CSV files:
setlocal enabledelayedexpansion
set count=0
for %%f in (cosmic_results\*_cosmic_points.csv) do (
    set /a count+=1
    echo !count!. %%~nxf
    set file!count!=%%f
)

if %count%==0 (
    echo No COSMIC function point files found!
    echo Please generate files first.
    pause
    goto menu
)

echo.
set /p choice="Select file number (1-%count%): "

if %choice% gtr %count% (
    echo Invalid choice!
    pause
    goto menu
)

call set filename=%%file%choice%%%
echo.
echo Checking file: %filename%
python base_script_files\cosmic_checker.py --file %filename%
pause
endlocal
goto menu

:convert
echo.
echo Available CSV files:
setlocal enabledelayedexpansion
set count=0
for %%f in (cosmic_results\*_cosmic_points.csv) do (
    set /a count+=1
    echo !count!. %%~nxf
    set file!count!=%%f
)

if %count%==0 (
    echo No COSMIC function point files found!
    echo Please generate files first.
    pause
    goto menu
)

echo.
set /p choice="Select file number (1-%count%): "

if %choice% gtr %count% (
    echo Invalid choice!
    pause
    goto menu
)

call set filename=%%file%choice%%%
echo.
echo Converting file: %filename%
python base_script_files\csv_to_xlsx_merger.py --input %filename%
pause
endlocal
goto menu

:stats
echo.
echo Available CSV files:
setlocal enabledelayedexpansion
set count=0
for %%f in (cosmic_results\*_cosmic_points.csv) do (
    set /a count+=1
    echo !count!. %%~nxf
    set file!count!=%%f
)

if %count%==0 (
    echo No COSMIC function point files found!
    echo Please generate files first.
    pause
    goto menu
)

echo.
set /p choice="Select file number (1-%count%): "

if %choice% gtr %count% (
    echo Invalid choice!
    pause
    goto menu
)

call set filename=%%file%choice%%%
echo.
echo Generating statistics for: %filename%
python base_script_files\cosmic_stats.py %filename%
pause
endlocal
goto menu

:exit
echo.
echo Thank you for using COSMIC Generator!
pause
exit
