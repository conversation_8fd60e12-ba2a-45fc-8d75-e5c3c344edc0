# COSMIC功能点生成与检查工具套件

这是一个完整的COSMIC功能点生成、检查和转换工具套件，支持从设计文档自动生成符合COSMIC规范的功能点文档。

## 🚀 快速开始

### 方式一：使用批处理文件（推荐）

双击 `启动COSMIC功能点生成器.bat` 文件，按照菜单提示操作：

1. **生成COSMIC功能点** - 交互式选择生成器类型
2. **检查COSMIC功能点规范** - 验证生成的功能点是否符合规范
3. **转换CSV为Excel格式** - 生成带单元格合并的Excel文件
4. **生成统计报告** - 查看功能点统计信息
5. **分析设计文档** - 从设计文档自动生成新的生成器

### 方式二：使用命令行

```bash
# 查看可用生成器
python generate_cosmic.py --list

# 交互式生成
python generate_cosmic.py --interactive

# 生成特定类型
python generate_cosmic.py --type cmcc_wallet

# 生成所有类型
python generate_cosmic.py --all
```

## 📋 功能特性

### 🔧 COSMIC功能点生成
- **动态生成器发现** - 自动扫描并加载所有可用的生成器
- **交互式选择** - 用户友好的选择界面
- **规范验证** - 自动验证生成的功能点是否符合COSMIC规范
- **批量生成** - 支持一次生成多个系统的功能点

### 📖 设计文档分析
- **Markdown文档解析** - 自动分析设计文档结构
- **智能功能识别** - 根据章节内容自动识别功能过程
- **生成器自动创建** - 从分析结果自动生成对应的生成器脚本

### 🔍 规范检查
- **完整性检查** - 验证必填字段、数据移动类型等
- **唯一性验证** - 确保数据属性和子过程描述的唯一性
- **规范性验证** - 检查是否使用禁用关键词
- **逻辑性检查** - 验证功能过程的逻辑完整性

### 📊 格式转换与统计
- **Excel转换** - 生成带单元格合并的专业Excel文件
- **统计报告** - 提供详细的功能点统计分析
- **多格式支持** - 支持CSV、Excel等多种格式

## 安装要求

- Python >= 3.9
- pandas >= 1.3.0
- openpyxl >= 3.0.0

```bash
pip install pandas openpyxl
```

## 工具脚本说明

### 1. COSMIC功能点生成器 (`generate_cosmic_points.py`)

**功能**: 自动生成符合COSMIC规范的功能点CSV文件

**特性**:
- 自动规则验证（E开头、EW/EX组合、子过程数量等）
- 智能关键词替换（避免禁用词汇）
- 唯一性保证（数据属性、子过程描述等）
- 合并单元格处理（功能过程列）

**使用方法**:
```bash
# 生成COSMIC功能点
python generate_cosmic_points.py

# 输出文件: cosmic_points.csv
```

**输出**:
- 48个功能点，48CFP
- 0个错误，符合所有COSMIC规范
- 自动生成统计报告

### 2. COSMIC功能点检查器 (`cosmic_checker.py`)

**功能**: 检查COSMIC功能点文件是否符合规范

**检查规则**:
- 基本规则（必要列、空值检查）
- 功能过程规则（首个子过程为E、包含EW或EX组合）
- 重复性规则（功能过程、子过程描述、数据组合重复）
- 内容规则（禁用关键词、相似度检查）
- CFP值规则（1、0.33、0的有效值）

**使用方法**:
```bash
# 基本检查
python cosmic_checker.py --file cosmic_points.csv

# 详细检查（显示详细日志）
python cosmic_checker.py --file cosmic_points.csv --verbose

# 保存检查结果到JSON文件
python cosmic_checker.py --file cosmic_points.csv --output check_result.json

# 完整检查
python cosmic_checker.py --file cosmic_points.csv --verbose --output check_result.json
```

**输出示例**:
```
============================================================
COSMIC功能点检查结果
============================================================
📊 文件信息:
  总行数: 48
  总CFP: 48.0

🔍 检查结果:
  错误数量: 0
  警告数量: 3

✅ 检查通过，符合COSMIC规范！
```

### 3. CSV转XLSX转换器 (`csv_to_xlsx_merger.py`)

**功能**: 将CSV文件转换为格式化的XLSX文件，并实现智能单元格合并

**特性**:
- 相同内容单元格自动合并
- 功能过程列合并（非空与空白单元格合并）
- 专业格式化（表头样式、边框、对齐）
- 自动列宽调整

**使用方法**:
```bash
# 基本转换
python csv_to_xlsx_merger.py --input cosmic_points.csv --output cosmic_points.xlsx

# 简化用法（自动生成输出文件名）
python csv_to_xlsx_merger.py --input cosmic_points.csv

# 帮助信息
python csv_to_xlsx_merger.py --help
```

**输出**:
- 专业格式的Excel文件
- 48个合并区域
- 12个功能过程正确分组
- 可直接用于项目文档

### 4. 统计分析工具 (`cosmic_stats.py`)

**功能**: 生成COSMIC功能点的详细统计分析

**使用方法**:
```bash
# 分析默认文件
python cosmic_stats.py

# 分析指定文件
python cosmic_stats.py cosmic_points.csv
```

**输出示例**:
```
=== 第三方商城积分消费集成系统 COSMIC功能点统计 ===
总功能点数: 48 个
总CFP值: 48 CFP

=== 按功能过程统计 ===
移动端生成商城跳转链接: 4个子过程, 4CFP
移动端检索用户积分余额: 3个子过程, 3CFP
...

=== 按数据移动类型统计 ===
E: 12个
R: 12个
X: 12个
W: 12个

=== 按平台统计 ===
移动端: 24个功能点
PC端: 24个功能点
```

## 文件说明

### 核心文件
- **`cosmic_points.csv`** - 最终的COSMIC功能点文件（48个功能点，48CFP，0错误）
- **`cosmic_points.xlsx`** - 格式化的Excel文件，包含完整的单元格合并
- **`cosmic_checker.py`** - COSMIC功能点检查工具
- **`generate_cosmic_points.py`** - 改进的COSMIC功能点生成器
- **`csv_to_xlsx_merger.py`** - CSV转XLSX转换工具

### 辅助文件
- **`cosmic_stats.py`** - 统计分析脚本
- **`cosmic_rule.md`** - COSMIC规则文档
- **`final_mall_integration_design.md`** - 最终设计文档

### 结果文件
- **`final_check_result.json`** - 最终检查结果

## 快速开始

### 1. 生成新的COSMIC功能点
```bash
# 生成符合规范的COSMIC功能点
python generate_cosmic_points.py

# 检查生成的文件
python cosmic_checker.py --file cosmic_points.csv --verbose

# 转换为Excel格式
python csv_to_xlsx_merger.py --input cosmic_points.csv
```

### 2. 检查现有COSMIC文件
```bash
# 检查现有的COSMIC文件
python cosmic_checker.py --file cosmic_points.csv --verbose --output check_result.json

# 生成统计报告
python cosmic_stats.py cosmic_points.csv
```

### 3. 转换格式
```bash
# 将CSV转换为专业格式的Excel文件
python csv_to_xlsx_merger.py --input cosmic_points.csv --output cosmic_points.xlsx
```

## COSMIC规则验证

本项目的检查工具基于以下COSMIC规则：

### 基本规则
- 必要列存在性检查
- 空值完整性验证
- CFP值有效性（1、0.33、0）

### 功能过程规则
- 第一个子过程必须是E类型
- 必须包含E和（W或X）的组合
- 子过程数量建议3-8个

### 重复性规则
- 功能过程名称唯一性
- 子过程描述唯一性
- 数据组合唯一性

### 内容规则
- 禁用关键词检查
- 子过程描述相似度分析
- 跨层交互验证

## 项目特色

### 1. 完全自动化
- 从生成到检查到格式化的完整流程
- 零手工干预，确保一致性
- 自动规则验证和错误修复

### 2. 高质量保证
- 生成的功能点通过所有COSMIC规范检查
- 智能避免常见错误（重复、禁用词汇等）
- 专业的Excel格式输出

### 3. 易于使用
- 简单的命令行接口
- 详细的使用文档
- 完整的示例和说明

## 使用场景

本项目适用于：
- **项目工作量估算**：基于COSMIC方法进行准确估算
- **开发成本评估**：CFP值可直接用于成本计算
- **项目规划参考**：功能点分解有助于项目规划
- **质量管理依据**：规范化的功能点文档
- **团队培训**：COSMIC方法学习和实践

## 技术架构

```
生成器 (generate_cosmic_points.py)
    ↓
CSV文件 (cosmic_points.csv)
    ↓
检查器 (cosmic_checker.py) → 验证报告
    ↓
转换器 (csv_to_xlsx_merger.py)
    ↓
Excel文件 (cosmic_points.xlsx) → 项目文档
```

## 贡献指南

欢迎贡献代码和建议：

1. Fork本项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目遵循MIT许可证。
