#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础COSMIC功能点生成器
提供通用的COSMIC功能点生成框架
"""

import csv
import os
from abc import ABC, abstractmethod
from typing import List, Dict, Any

class BaseCosmicGenerator(ABC):
    """基础COSMIC功能点生成器"""
    
    def __init__(self, subsystem: str, module_level2: str, module_level3: str):
        self.cosmic_points = []
        self.revision_id = "1"
        self.subsystem = subsystem
        self.module_level2 = module_level2
        self.module_level3 = module_level3
        self.row_counter = 0
        
        # COSMIC规则约束
        self.forbidden_subprocess_keywords = ['查询', '显示', '展示', '列表', '界面', '页面', '菜单', '按钮']
        self.forbidden_datagroup_keywords = ['查询', '显示', '展示', '列表', '界面', '页面', '菜单', '按钮']
        self.forbidden_dataattr_keywords = ['信息', '数据', '内容', '配置', '详情', '列表', '结果']
        
        # 数据属性替换映射
        self.dataattr_replacements = {
            '内容': '值',
            '详情': '明细',
            '结果': '输出',
            '数据': '值',
            '配置': '参数',
            '信息': '数据',
            '列表': '集合'
        }
    
    def _get_unique_data_attributes(self, base_attrs: str, platform: str, context: str) -> str:
        """生成唯一的数据属性，避免重复和禁用关键词"""
        self.row_counter += 1
        
        # 替换禁用关键词
        cleaned_attrs = base_attrs
        for forbidden, replacement in self.dataattr_replacements.items():
            cleaned_attrs = cleaned_attrs.replace(forbidden, replacement)
        
        # 添加平台前缀、上下文和行号确保唯一性
        unique_attrs = f"{platform}{context}{cleaned_attrs},行号{self.row_counter}"
        return unique_attrs
    
    def _create_subprocess_description(self, action: str, platform: str) -> str:
        """创建符合COSMIC规范的子过程描述"""
        # 避免使用禁用关键词，使用动词+对象的形式
        action_mapping = {
            '输入': '接收',
            '读取': '获取', 
            '输出': '传输',
            '保存': '存储',
            '更新': '修改',
            '生成': '创建',
            '查询': '检索',
            '配置': '设置'
        }
        
        for forbidden, replacement in action_mapping.items():
            if forbidden in action:
                action = action.replace(forbidden, replacement)
        
        return f"{platform}{action}"
    
    def _create_data_group(self, base_name: str, platform: str) -> str:
        """创建数据组名称，避免禁用关键词"""
        # 替换禁用关键词
        replacements = {
            '信息': '数据',
            '详情': '明细', 
            '列表': '集合',
            '结果': '输出',
            '内容': '值',
            '配置': '参数'
        }
        
        for forbidden, replacement in replacements.items():
            if forbidden in base_name:
                base_name = base_name.replace(forbidden, replacement)
        
        return f"{platform}{base_name}"
    
    def add_functional_process(self, platform: str, functional_user: str, user_requirement: str, 
                             trigger_event: str, process_name: str, subprocesses: List[Dict[str, Any]]):
        """添加一个完整的功能过程"""
        
        # 验证功能过程规则
        if not subprocesses:
            raise ValueError("功能过程必须包含至少一个子过程")
        
        # 检查第一个子过程是否为E
        if subprocesses[0]['move_type'] != 'E':
            raise ValueError("功能过程的第一个子过程必须是E类型")
        
        # 检查是否包含必要的数据移动类型
        move_types = {sp['move_type'] for sp in subprocesses}
        if 'E' not in move_types:
            raise ValueError("功能过程必须包含E类型")
        if not ('W' in move_types or 'X' in move_types):
            raise ValueError("功能过程必须包含W或X类型")
        
        # 检查子过程数量
        if len(subprocesses) < 3:
            print(f"警告：功能过程 '{process_name}' 子过程数量过少（{len(subprocesses)}个），建议至少3个")
        elif len(subprocesses) > 8:
            print(f"警告：功能过程 '{process_name}' 子过程数量过多（{len(subprocesses)}个），建议不超过8个")
        
        for i, subprocess in enumerate(subprocesses):
            # 第一行包含功能过程名称，其他行为空（合并单元格效果）
            process_name_cell = process_name if i == 0 else ""
            
            # 创建符合规范的子过程描述
            subprocess_desc = self._create_subprocess_description(
                subprocess['description'], platform
            )
            
            # 创建数据组
            data_group = self._create_data_group(subprocess['data_group'], platform)
            
            # 创建唯一的数据属性
            data_attributes = self._get_unique_data_attributes(
                subprocess['data_attributes'], platform, subprocess['context']
            )
            
            point = {
                "修订标识": self.revision_id,
                "子系统": self.subsystem,
                "一级模块": platform,
                "二级模块": self.module_level2,
                "三级模块": self.module_level3,
                "功能用户": functional_user,
                "功能用户需求": user_requirement,
                "触发事件": trigger_event,
                "功能过程": process_name_cell,
                "子过程描述": subprocess_desc,
                "数据移动类型": subprocess['move_type'],
                "数据组": data_group,
                "数据属性": data_attributes,
                "CFP": subprocess.get('cfp', 1),
                "CFP核定": "",
                "评定依据": ""
            }
            
            self.cosmic_points.append(point)
    
    @abstractmethod
    def generate_functional_processes(self):
        """生成具体的功能过程 - 子类必须实现"""
        pass
    
    def save_to_csv(self, filename: str):
        """保存COSMIC功能点到CSV文件"""
        
        if not self.cosmic_points:
            print("没有生成任何功能点")
            return
        
        # CSV文件头
        headers = [
            "修订标识", "子系统", "一级模块", "二级模块", "三级模块",
            "功能用户", "功能用户需求", "触发事件", "功能过程", "子过程描述",
            "数据移动类型", "数据组", "数据属性", "CFP", "CFP核定", "评定依据"
        ]
        
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            writer.writerows(self.cosmic_points)
        
        print(f"COSMIC功能点已保存到: {filename}")
        print(f"总功能点数: {len(self.cosmic_points)}")
        print(f"总CFP值: {sum(point['CFP'] for point in self.cosmic_points)}")
    
    def validate_cosmic_rules(self):
        """验证生成的COSMIC功能点是否符合规则"""
        print("正在验证COSMIC规则...")
        
        # 检查数据属性唯一性
        data_attrs = [point['数据属性'] for point in self.cosmic_points]
        if len(data_attrs) != len(set(data_attrs)):
            print("警告：存在重复的数据属性")
        else:
            print("✓ 数据属性唯一性检查通过")
        
        # 检查子过程描述唯一性
        subprocess_descs = [point['子过程描述'] for point in self.cosmic_points]
        if len(subprocess_descs) != len(set(subprocess_descs)):
            print("警告：存在重复的子过程描述")
        else:
            print("✓ 子过程描述唯一性检查通过")
        
        # 检查禁用关键词
        error_count = 0
        for point in self.cosmic_points:
            for keyword in self.forbidden_dataattr_keywords:
                if keyword in point['数据属性']:
                    print(f"错误：数据属性包含禁用关键词 '{keyword}': {point['数据属性']}")
                    error_count += 1
        
        if error_count == 0:
            print("✓ 禁用关键词检查通过")
        else:
            print(f"发现 {error_count} 个禁用关键词问题")
    
    def print_statistics(self):
        """打印统计信息"""
        print(f"\n=== {self.module_level3} COSMIC功能点统计 ===")
        
        # 按平台统计
        platform_stats = {}
        for point in self.cosmic_points:
            platform = point['一级模块']
            if platform not in platform_stats:
                platform_stats[platform] = 0
            platform_stats[platform] += 1
        
        print("\n按平台统计:")
        for platform, count in platform_stats.items():
            print(f"  {platform}: {count}个功能点")
        
        # 按数据移动类型统计
        move_type_stats = {}
        for point in self.cosmic_points:
            move_type = point['数据移动类型']
            if move_type not in move_type_stats:
                move_type_stats[move_type] = 0
            move_type_stats[move_type] += 1
        
        print("\n按数据移动类型统计:")
        for move_type, count in move_type_stats.items():
            print(f"  {move_type}: {count}个")
        
        # 按功能用户统计
        user_stats = {}
        for point in self.cosmic_points:
            user = point['功能用户']
            if user not in user_stats:
                user_stats[user] = 0
            user_stats[user] += 1
        
        print("\n按功能用户统计:")
        for user, count in user_stats.items():
            print(f"  {user}: {count}个功能点")
    
    def generate(self, output_filename: str):
        """完整的生成流程"""
        print(f"开始生成{self.module_level3}COSMIC功能点...")
        
        # 生成功能过程
        self.generate_functional_processes()
        
        # 验证规则
        self.validate_cosmic_rules()
        
        # 打印统计
        self.print_statistics()
        
        # 保存到文件
        self.save_to_csv(output_filename)
        
        print("COSMIC功能点生成完成！")
        return output_filename
