#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
COSMIC功能点CSV转XLSX工具
将CSV文件转换为XLSX格式，并实现智能单元格合并
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Alignment, Border, Side, Font, PatternFill
from openpyxl.utils.dataframe import dataframe_to_rows
import argparse
import os

class CosmicCsvToXlsxConverter:
    """COSMIC CSV转XLSX转换器"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
        
        # 样式定义
        self.header_font = Font(bold=True, size=11)
        self.header_fill = PatternFill(start_color="D9E1F2", end_color="D9E1F2", fill_type="solid")
        self.border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        self.center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        self.left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    def load_csv(self, csv_file: str) -> pd.DataFrame:
        """加载CSV文件"""
        try:
            df = pd.read_csv(csv_file, encoding='utf-8')
            print(f"成功加载CSV文件: {csv_file}")
            print(f"数据行数: {len(df)}")
            print(f"列数: {len(df.columns)}")
            return df
        except Exception as e:
            print(f"加载CSV文件失败: {e}")
            raise
    
    def create_xlsx_workbook(self, df: pd.DataFrame):
        """创建XLSX工作簿"""
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.active
        self.worksheet.title = "COSMIC功能点"
        
        # 写入数据
        for r in dataframe_to_rows(df, index=False, header=True):
            self.worksheet.append(r)
        
        print("XLSX工作簿创建完成")
    
    def apply_header_style(self):
        """应用表头样式"""
        for cell in self.worksheet[1]:
            cell.font = self.header_font
            cell.fill = self.header_fill
            cell.alignment = self.center_alignment
            cell.border = self.border
        
        print("表头样式应用完成")
    
    def merge_cells_by_content(self, df: pd.DataFrame):
        """根据内容合并单元格"""
        
        # 获取功能过程列的索引（从1开始，因为Excel是1基索引）
        process_col_index = None
        for idx, col in enumerate(df.columns):
            if col == '功能过程':
                process_col_index = idx + 1  # Excel列索引从1开始
                break
        
        if process_col_index is None:
            print("未找到'功能过程'列")
            return
        
        print(f"功能过程列索引: {process_col_index}")
        
        # 1. 合并功能过程列之前的列（相同内容合并）
        self._merge_columns_before_process(df, process_col_index)
        
        # 2. 合并功能过程列（非空单元格与下方空白单元格合并）
        self._merge_process_column(df, process_col_index)
    
    def _merge_columns_before_process(self, df: pd.DataFrame, process_col_index: int):
        """合并功能过程列之前的列"""
        print("开始合并功能过程列之前的列...")
        
        # 遍历功能过程列之前的每一列
        for col_idx in range(1, process_col_index):
            col_name = df.columns[col_idx - 1]
            print(f"处理列: {col_name} (第{col_idx}列)")
            
            # 获取该列的所有值
            values = df.iloc[:, col_idx - 1].tolist()
            
            # 查找连续相同值的区间
            merge_ranges = self._find_consecutive_same_values(values)
            
            # 执行合并
            for start_row, end_row in merge_ranges:
                if start_row != end_row:  # 只有当范围大于1行时才合并
                    # Excel行号从2开始（第1行是表头）
                    excel_start_row = start_row + 2
                    excel_end_row = end_row + 2
                    
                    try:
                        self.worksheet.merge_cells(
                            start_row=excel_start_row,
                            start_column=col_idx,
                            end_row=excel_end_row,
                            end_column=col_idx
                        )
                        print(f"  合并 {col_name} 第{excel_start_row}-{excel_end_row}行")
                    except Exception as e:
                        print(f"  合并失败: {e}")
    
    def _merge_process_column(self, df: pd.DataFrame, process_col_index: int):
        """合并功能过程列"""
        print("开始合并功能过程列...")
        
        # 获取功能过程列的值
        process_values = df['功能过程'].tolist()
        
        # 查找需要合并的区间（非空值与后续空值）
        merge_ranges = self._find_process_merge_ranges(process_values)
        
        # 执行合并
        for start_row, end_row in merge_ranges:
            if start_row != end_row:  # 只有当范围大于1行时才合并
                # Excel行号从2开始（第1行是表头）
                excel_start_row = start_row + 2
                excel_end_row = end_row + 2
                
                try:
                    self.worksheet.merge_cells(
                        start_row=excel_start_row,
                        start_column=process_col_index,
                        end_row=excel_end_row,
                        end_column=process_col_index
                    )
                    print(f"  合并功能过程 第{excel_start_row}-{excel_end_row}行")
                except Exception as e:
                    print(f"  功能过程合并失败: {e}")
    
    def _find_consecutive_same_values(self, values: list) -> list:
        """查找连续相同值的区间"""
        if not values:
            return []
        
        ranges = []
        start = 0
        
        for i in range(1, len(values)):
            # 如果当前值与前一个值不同，或者到达末尾
            if values[i] != values[i-1]:
                if start != i-1:  # 有连续相同的值
                    ranges.append((start, i-1))
                start = i
        
        # 处理最后一个区间
        if start != len(values)-1:
            ranges.append((start, len(values)-1))
        
        return ranges
    
    def _find_process_merge_ranges(self, process_values: list) -> list:
        """查找功能过程列需要合并的区间"""
        if not process_values:
            return []
        
        ranges = []
        i = 0
        
        while i < len(process_values):
            # 如果当前值不为空且不是NaN
            if pd.notna(process_values[i]) and str(process_values[i]).strip():
                start = i
                # 查找后续的空值
                j = i + 1
                while j < len(process_values) and (pd.isna(process_values[j]) or not str(process_values[j]).strip()):
                    j += 1
                
                # 如果有后续空值，则添加合并范围
                if j > i + 1:
                    ranges.append((start, j - 1))
                
                i = j
            else:
                i += 1
        
        return ranges
    
    def apply_cell_styles(self, df: pd.DataFrame):
        """应用单元格样式"""
        print("应用单元格样式...")
        
        # 为所有单元格添加边框和对齐方式
        for row in range(1, len(df) + 2):  # +2因为有表头
            for col in range(1, len(df.columns) + 1):
                cell = self.worksheet.cell(row=row, column=col)
                cell.border = self.border
                
                # 根据列的内容设置对齐方式
                if col <= 9:  # 前9列（到功能过程列）居中对齐
                    cell.alignment = self.center_alignment
                else:  # 其他列左对齐
                    cell.alignment = self.left_alignment
    
    def adjust_column_widths(self, df: pd.DataFrame):
        """调整列宽"""
        print("调整列宽...")
        
        # 列宽设置
        column_widths = {
            'A': 8,   # 修订标识
            'B': 12,  # 子系统
            'C': 12,  # 一级模块
            'D': 12,  # 二级模块
            'E': 18,  # 三级模块
            'F': 12,  # 功能用户
            'G': 25,  # 功能用户需求
            'H': 25,  # 触发事件
            'I': 25,  # 功能过程
            'J': 30,  # 子过程描述
            'K': 12,  # 数据移动类型
            'L': 20,  # 数据组
            'M': 40,  # 数据属性
            'N': 8,   # CFP
            'O': 10,  # CFP核定
            'P': 15   # 评定依据
        }
        
        for col_letter, width in column_widths.items():
            self.worksheet.column_dimensions[col_letter].width = width
    
    def save_xlsx(self, output_file: str):
        """保存XLSX文件"""
        try:
            self.workbook.save(output_file)
            print(f"XLSX文件已保存: {output_file}")
        except Exception as e:
            print(f"保存XLSX文件失败: {e}")
            raise
    
    def convert(self, csv_file: str, xlsx_file: str = None):
        """执行转换"""
        if xlsx_file is None:
            xlsx_file = csv_file.replace('.csv', '.xlsx')
        
        print(f"开始转换: {csv_file} -> {xlsx_file}")
        
        # 1. 加载CSV文件
        df = self.load_csv(csv_file)
        
        # 2. 创建XLSX工作簿
        self.create_xlsx_workbook(df)
        
        # 3. 应用表头样式
        self.apply_header_style()
        
        # 4. 合并单元格
        self.merge_cells_by_content(df)
        
        # 5. 应用单元格样式
        self.apply_cell_styles(df)
        
        # 6. 调整列宽
        self.adjust_column_widths(df)
        
        # 7. 保存文件
        self.save_xlsx(xlsx_file)
        
        print("转换完成！")
        return xlsx_file


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="COSMIC功能点CSV转XLSX工具")
    parser.add_argument("--input", "-i", required=True, help="输入的CSV文件路径")
    parser.add_argument("--output", "-o", help="输出的XLSX文件路径（可选）")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"错误：输入文件不存在: {args.input}")
        return
    
    converter = CosmicCsvToXlsxConverter()
    converter.convert(args.input, args.output)


if __name__ == "__main__":
    main()
