#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cosmic功能点转换为需求说明文档生成器
读取xlsx文件内容，转换为标准的需求说明markdown格式

功能说明:
- 读取Cosmic功能点Excel文件
- 按照层级结构(一级模块->二级模块->三级模块->功能用户需求)组织内容
- 生成符合规范的需求说明Markdown文档
- 包含触发事件、功能过程和子过程描述

使用方法:
1. 直接运行: python cosmic_to_requirement.py
   (使用默认路径: ../cosmic_results/allcosmic-20250703.xlsx)

2. 指定文件: python cosmic_to_requirement.py path/to/your/file.xlsx

输出格式:
## 1 业务功能需求
### 1.1 一级模块
#### 1.1.1 二级模块
##### 1.1.1.1 三级模块
  - 功能用户需求
    触发事件为xxx，功能过程为xxx，包含以下子过程：

    1.子过程描述1
    2.子过程描述2
    ...

作者: AI Assistant
版本: 1.0
"""

import pandas as pd
import os
from pathlib import Path
from typing import Dict, List, Any
import re


class CosmicToRequirementConverter:
    """Cosmic功能点转需求说明转换器"""
    
    def __init__(self):
        self.data = None
        self.output_content = []
        
    def read_excel_file(self, excel_path: str) -> bool:
        """读取Excel文件"""
        try:
            # 尝试读取Excel文件，跳过前几行可能的标题行
            self.data = pd.read_excel(excel_path, header=1)  # 从第二行开始读取
            print(f"成功读取Excel文件: {excel_path}")
            print(f"数据行数: {len(self.data)}")
            print(f"列名: {list(self.data.columns)}")

            # 如果列名还是不对，尝试其他行作为标题
            if '一级模块' not in self.data.columns:
                print("尝试从第3行开始读取...")
                self.data = pd.read_excel(excel_path, header=2)
                print(f"新列名: {list(self.data.columns)}")

            # 如果还是不对，尝试第4行
            if '一级模块' not in self.data.columns:
                print("尝试从第4行开始读取...")
                self.data = pd.read_excel(excel_path, header=3)
                print(f"新列名: {list(self.data.columns)}")

            # 显示前几行数据用于调试
            print("前5行数据:")
            print(self.data.head())

            # 显示数据概览
            print(f"\n数据概览:")
            print(f"- 非空一级模块数: {self.data['一级模块'].notna().sum()}")
            print(f"- 非空功能用户需求数: {self.data['功能用户需求'].notna().sum()}")
            print(f"- 非空子过程描述数: {self.data['子过程描述'].notna().sum()}")

            return True
        except Exception as e:
            print(f"读取Excel文件失败: {e}")
            return False
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容，去除多余空格和换行"""
        if pd.isna(text) or text is None:
            return ""
        return str(text).strip().replace('\n', ' ').replace('\r', ' ')
    
    def _group_data_by_hierarchy(self) -> Dict[str, Any]:
        """按层级结构分组数据"""
        grouped_data = {}
        current_requirement_key = None
        processed_count = 0

        for _, row in self.data.iterrows():
            # 获取层级信息
            level1 = self._clean_text(row.get('一级模块', ''))
            level2 = self._clean_text(row.get('二级模块', ''))
            level3 = self._clean_text(row.get('三级模块', ''))
            user_requirement = self._clean_text(row.get('功能用户需求', ''))
            trigger_event = self._clean_text(row.get('触发事件', ''))
            process_name = self._clean_text(row.get('功能过程', ''))
            subprocess_desc = self._clean_text(row.get('子过程描述', ''))

            # 如果有新的功能用户需求，更新当前需求键
            if user_requirement and level1 and level2 and level3:
                current_requirement_key = (level1, level2, level3, user_requirement, trigger_event, process_name)

            # 如果没有层级信息但有子过程描述，使用当前需求键
            if not level1 and not level2 and not level3 and subprocess_desc and current_requirement_key:
                level1, level2, level3, user_requirement, trigger_event, process_name = current_requirement_key

            # 如果有层级信息但没有用户需求，跳过
            if (level1 or level2 or level3) and not user_requirement:
                continue

            # 跳过完全空的行
            if not level1 or not level2 or not level3 or not user_requirement:
                continue

            # 构建层级结构
            if level1 not in grouped_data:
                grouped_data[level1] = {}
            if level2 not in grouped_data[level1]:
                grouped_data[level1][level2] = {}
            if level3 not in grouped_data[level1][level2]:
                grouped_data[level1][level2][level3] = {}
            if user_requirement not in grouped_data[level1][level2][level3]:
                grouped_data[level1][level2][level3][user_requirement] = {
                    'trigger_event': trigger_event,
                    'process_name': process_name,
                    'subprocesses': []
                }

            # 添加子过程（如果有新的触发事件或过程名称，更新它们）
            if trigger_event:
                grouped_data[level1][level2][level3][user_requirement]['trigger_event'] = trigger_event
            if process_name:
                grouped_data[level1][level2][level3][user_requirement]['process_name'] = process_name

            if subprocess_desc:
                grouped_data[level1][level2][level3][user_requirement]['subprocesses'].append(subprocess_desc)

            processed_count += 1

        print(f"处理了 {processed_count} 行数据")
        return grouped_data
    
    def _generate_markdown_content(self, grouped_data: Dict[str, Any]) -> str:
        """生成Markdown内容"""
        content = []
        content.append("## 1 业务功能需求")
        
        level1_counter = 1
        for level1, level2_data in grouped_data.items():
            content.append(f"### 1.{level1_counter} {level1}")
            
            level2_counter = 1
            for level2, level3_data in level2_data.items():
                content.append(f"#### 1.{level1_counter}.{level2_counter} {level2}")
                
                level3_counter = 1
                for level3, requirements in level3_data.items():
                    content.append(f"##### 1.{level1_counter}.{level2_counter}.{level3_counter} {level3}")
                    
                    for user_requirement, details in requirements.items():
                        if not user_requirement:
                            continue
                            
                        content.append(f"  - {user_requirement}")
                        
                        # 添加触发事件和功能过程描述
                        trigger_event = details.get('trigger_event', '')
                        process_name = details.get('process_name', '')
                        
                        if trigger_event and process_name:
                            content.append(f"    触发事件为{trigger_event}，功能过程为{process_name}，包含以下子过程：")
                        elif trigger_event:
                            content.append(f"    触发事件为{trigger_event}，包含以下子过程：")
                        elif process_name:
                            content.append(f"    功能过程为{process_name}，包含以下子过程：")
                        else:
                            content.append("    包含以下子过程：")

                        # 添加子过程列表（取消上面的空行，数字后面添加空格）
                        subprocesses = details.get('subprocesses', [])
                        for i, subprocess in enumerate(subprocesses, 1):
                            if subprocess:
                                content.append(f"    {i}. {subprocess}")

                        content.append("")  # 空行
                    
                    level3_counter += 1
                
                level2_counter += 1
            
            level1_counter += 1
        
        return '\n'.join(content)
    
    def convert_to_requirement_doc(self, excel_path: str) -> str:
        """转换Excel文件为需求说明文档"""
        # 读取Excel文件
        if not self.read_excel_file(excel_path):
            return ""
        
        # 按层级分组数据
        grouped_data = self._group_data_by_hierarchy()
        
        # 生成Markdown内容
        markdown_content = self._generate_markdown_content(grouped_data)
        
        return markdown_content
    
    def save_requirement_doc(self, excel_path: str, output_path: str = None) -> str:
        """保存需求说明文档"""
        # 转换内容
        content = self.convert_to_requirement_doc(excel_path)

        if not content:
            print("❌ 转换失败，无内容生成")
            return ""

        # 确定输出路径
        if output_path is None:
            excel_file = Path(excel_path)
            output_path = excel_file.parent / f"{excel_file.stem}_需求说明.md"

        # 保存文件
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📝 需求说明文档已保存到: {output_path}")

            # 统计信息
            lines = content.count('\n') + 1
            requirements_count = content.count('  - ')
            subprocesses_count = content.count('    1.') + content.count('    2.') + content.count('    3.') + content.count('    4.') + content.count('    5.') + content.count('    6.') + content.count('    7.') + content.count('    8.')

            print(f"📊 统计信息:")
            print(f"   - 总行数: {lines}")
            print(f"   - 功能需求数: {requirements_count}")
            print(f"   - 子过程数: {subprocesses_count}")

            return str(output_path)
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return ""


def main():
    """主函数"""
    import sys

    # 支持命令行参数
    if len(sys.argv) > 1:
        excel_path = sys.argv[1]
    else:
        # 默认Excel文件路径（相对于项目根目录）
        excel_path = "../cosmic_results/allcosmic-20250703.xlsx"

    # 检查文件是否存在
    if not os.path.exists(excel_path):
        print(f"Excel文件不存在: {excel_path}")
        # 尝试绝对路径
        abs_path = os.path.abspath(excel_path)
        print(f"尝试的绝对路径: {abs_path}")
        print("\n使用方法:")
        print("  python cosmic_to_requirement.py [excel_file_path]")
        print("  例如: python cosmic_to_requirement.py ../cosmic_results/allcosmic-20250703.xlsx")
        return

    # 创建转换器
    converter = CosmicToRequirementConverter()

    # 转换并保存
    output_path = converter.save_requirement_doc(excel_path)

    if output_path:
        print(f"\n✅ 转换完成！")
        print(f"📄 输出文件: {output_path}")
        print(f"📊 Excel文件: {excel_path}")
    else:
        print("❌ 转换失败！")


if __name__ == "__main__":
    main()
