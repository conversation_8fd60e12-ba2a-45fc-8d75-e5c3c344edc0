#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
平台运营与商业化中心 COSMIC功能点生成器
基于设计文档生成符合COSMIC规范的功能点
"""

import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class PlatformOperationCenterGenerator(BaseCosmicGenerator):
    """平台运营与商业化中心COSMIC功能点生成器"""
    
    def __init__(self):
        super().__init__(
            subsystem="智慧社区",
            module_level2="平台运营与商业化中心", 
            module_level3="平台收益中心"
        )
    
    def generate_functional_processes(self):
        """生成平台运营与商业化中心的所有功能过程"""
        
        platforms = ["PC端", "移动端"]
        
        for platform in platforms:
            # 8.1 平台收益中心 - 收益管理仪表盘
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="希望一登录就能看到平台整体和各社区的收益概况",
                trigger_event="登录收益管理后台",
                process_name=f"{platform}加载收益管理仪表盘",
                subprocesses=[
                    {
                        'description': '接收仪表盘数据请求',
                        'move_type': 'E',
                        'data_group': '仪表盘数据请求',
                        'data_attributes': '用户ID,请求时间,查询范围',
                        'context': '仪表盘数据请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取收益统计数据',
                        'move_type': 'R',
                        'data_group': '收益统计数据',
                        'data_attributes': '今日总收益,本月总收益,年度累计收益,待确认收益总额',
                        'context': '收益统计数据',
                        'cfp': 1
                    },
                    {
                        'description': '获取收益来源占比数据',
                        'move_type': 'R',
                        'data_group': '收益来源占比数据',
                        'data_attributes': '收益类型,占比百分比,金额',
                        'context': '收益来源占比数据',
                        'cfp': 1
                    },
                    {
                        'description': '获取社区收益排行数据',
                        'move_type': 'R',
                        'data_group': '社区收益排行数据',
                        'data_attributes': '社区ID,社区名称,收益金额,排名',
                        'context': '社区收益排行数据',
                        'cfp': 1
                    },
                    {
                        'description': '传输仪表盘视图数据',
                        'move_type': 'X',
                        'data_group': '仪表盘视图数据',
                        'data_attributes': '统计卡片数据,图表数据,趋势数据',
                        'context': '仪表盘视图数据',
                        'cfp': 1
                    }
                ]
            )
            
            # 收益流水管理
            self.add_functional_process(
                platform=platform,
                functional_user="平台财务管理员",
                user_requirement="需要看到所有收益明细并能进行审核确认",
                trigger_event="访问收益流水管理页面",
                process_name=f"{platform}管理收益流水",
                subprocesses=[
                    {
                        'description': '接收流水查询请求',
                        'move_type': 'E',
                        'data_group': '流水查询请求',
                        'data_attributes': '查询条件,筛选参数,分页参数',
                        'context': '流水查询请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取收益流水记录',
                        'move_type': 'R',
                        'data_group': '收益流水记录',
                        'data_attributes': '流水ID,来源单据ID,收益类型,金额,状态,录入方式',
                        'context': '收益流水记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输流水列表数据',
                        'move_type': 'X',
                        'data_group': '流水列表数据',
                        'data_attributes': '流水明细,总记录数,分页数据',
                        'context': '流水列表数据',
                        'cfp': 1
                    }
                ]
            )
            
            # 手动录入收益
            self.add_functional_process(
                platform=platform,
                functional_user="平台财务管理员",
                user_requirement="能够手动录入线下收益",
                trigger_event="点击录入新收益按钮",
                process_name=f"{platform}录入新收益",
                subprocesses=[
                    {
                        'description': '接收收益录入请求',
                        'move_type': 'E',
                        'data_group': '收益录入请求',
                        'data_attributes': '社区ID,收益类型,金额,交易日期,服务商ID,描述',
                        'context': '收益录入请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取社区和服务商验证数据',
                        'move_type': 'R',
                        'data_group': '社区服务商验证数据',
                        'data_attributes': '社区名称,服务商名称,关联状态',
                        'context': '社区服务商验证数据',
                        'cfp': 1
                    },
                    {
                        'description': '存储新收益流水',
                        'move_type': 'W',
                        'data_group': '新收益流水',
                        'data_attributes': '流水ID,录入方式,状态,创建人,创建时间',
                        'context': '新收益流水',
                        'cfp': 1
                    },
                    {
                        'description': '传输录入结果',
                        'move_type': 'X',
                        'data_group': '录入结果',
                        'data_attributes': '操作状态,流水ID,成功消息',
                        'context': '录入结果',
                        'cfp': 1
                    }
                ]
            )
            
            # 收益确认操作
            self.add_functional_process(
                platform=platform,
                functional_user="平台财务管理员",
                user_requirement="能够批量确认待审核的收益流水",
                trigger_event="选择流水并点击确认按钮",
                process_name=f"{platform}确认收益流水",
                subprocesses=[
                    {
                        'description': '接收流水确认请求',
                        'move_type': 'E',
                        'data_group': '流水确认请求',
                        'data_attributes': '流水ID列表,确认操作类型,操作人',
                        'context': '流水确认请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取待确认流水状态',
                        'move_type': 'R',
                        'data_group': '待确认流水状态',
                        'data_attributes': '流水ID,当前状态,金额,来源类型',
                        'context': '待确认流水状态',
                        'cfp': 1
                    },
                    {
                        'description': '修改流水确认状态',
                        'move_type': 'W',
                        'data_group': '流水确认状态',
                        'data_attributes': '流水ID,新状态,确认时间,确认人',
                        'context': '流水确认状态',
                        'cfp': 1
                    },
                    {
                        'description': '传输确认操作结果',
                        'move_type': 'X',
                        'data_group': '确认操作结果',
                        'data_attributes': '成功数量,失败数量,操作结果明细',
                        'context': '确认操作结果',
                        'cfp': 1
                    }
                ]
            )
            
            # 收益冲正操作
            self.add_functional_process(
                platform=platform,
                functional_user="平台财务管理员",
                user_requirement="能够对错误的收益流水进行冲正",
                trigger_event="点击冲正按钮并填写原因",
                process_name=f"{platform}执行收益冲正",
                subprocesses=[
                    {
                        'description': '接收冲正操作请求',
                        'move_type': 'E',
                        'data_group': '冲正操作请求',
                        'data_attributes': '原流水ID,冲正原因,操作人',
                        'context': '冲正操作请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取原始流水数据',
                        'move_type': 'R',
                        'data_group': '原始流水数据',
                        'data_attributes': '流水ID,金额,描述,状态,来源数据',
                        'context': '原始流水数据',
                        'cfp': 1
                    },
                    {
                        'description': '存储冲正流水记录',
                        'move_type': 'W',
                        'data_group': '冲正流水记录',
                        'data_attributes': '冲正流水ID,负向金额,冲正状态,创建时间',
                        'context': '冲正流水记录',
                        'cfp': 1
                    },
                    {
                        'description': '存储冲正日志',
                        'move_type': 'W',
                        'data_group': '冲正日志',
                        'data_attributes': '日志ID,原流水ID,冲正流水ID,冲正原因,操作人',
                        'context': '冲正日志',
                        'cfp': 1
                    },
                    {
                        'description': '传输冲正操作结果',
                        'move_type': 'X',
                        'data_group': '冲正操作结果',
                        'data_attributes': '操作状态,冲正流水ID,操作时间',
                        'context': '冲正操作结果',
                        'cfp': 1
                    }
                ]
            )

            # 8.2 生态分润中心 - 分润规则配置
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="能灵活配置不同业务场景下的分润比例",
                trigger_event="访问分润规则管理页面",
                process_name=f"{platform}管理分润规则",
                subprocesses=[
                    {
                        'description': '接收规则管理请求',
                        'move_type': 'E',
                        'data_group': '规则管理请求',
                        'data_attributes': '操作类型,规则ID,用户ID',
                        'context': '规则管理请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取分润规则列表',
                        'move_type': 'R',
                        'data_group': '分润规则列表',
                        'data_attributes': '规则ID,规则名称,适用收益类型,各方分成比例,是否默认',
                        'context': '分润规则列表',
                        'cfp': 1
                    },
                    {
                        'description': '传输规则管理视图',
                        'move_type': 'X',
                        'data_group': '规则管理视图',
                        'data_attributes': '规则列表数据,操作权限,界面配置',
                        'context': '规则管理视图',
                        'cfp': 1
                    }
                ]
            )

            # 创建分润规则
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="创建新的分润规则",
                trigger_event="点击新建规则按钮并提交表单",
                process_name=f"{platform}创建分润规则",
                subprocesses=[
                    {
                        'description': '接收规则创建请求',
                        'move_type': 'E',
                        'data_group': '规则创建请求',
                        'data_attributes': '规则名称,适用收益类型,平台分成比例,渠道分成比例,协助分成比例,风险准备金比例',
                        'context': '规则创建请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取规则冲突检查数据',
                        'move_type': 'R',
                        'data_group': '规则冲突检查数据',
                        'data_attributes': '收益类型,现有规则ID,规则状态',
                        'context': '规则冲突检查数据',
                        'cfp': 1
                    },
                    {
                        'description': '存储新分润规则',
                        'move_type': 'W',
                        'data_group': '新分润规则',
                        'data_attributes': '规则ID,创建时间,创建人,规则状态',
                        'context': '新分润规则',
                        'cfp': 1
                    },
                    {
                        'description': '传输创建结果',
                        'move_type': 'X',
                        'data_group': '创建结果',
                        'data_attributes': '操作状态,规则ID,成功消息',
                        'context': '创建结果',
                        'cfp': 1
                    }
                ]
            )

            # 月度分润计算
            self.add_functional_process(
                platform=platform,
                functional_user="系统定时任务",
                user_requirement="每月自动计算合作伙伴分润",
                trigger_event="每月1号凌晨定时触发",
                process_name=f"{platform}执行月度分润计算",
                subprocesses=[
                    {
                        'description': '接收分润计算触发',
                        'move_type': 'E',
                        'data_group': '分润计算触发',
                        'data_attributes': '计算周期,触发时间,任务ID',
                        'context': '分润计算触发',
                        'cfp': 1
                    },
                    {
                        'description': '获取上月收益流水',
                        'move_type': 'R',
                        'data_group': '上月收益流水',
                        'data_attributes': '流水ID,金额,收益类型,社区ID,状态',
                        'context': '上月收益流水',
                        'cfp': 1
                    },
                    {
                        'description': '获取合作伙伴关联数据',
                        'move_type': 'R',
                        'data_group': '合作伙伴关联数据',
                        'data_attributes': '伙伴ID,用户ID,伙伴类型,关联社区列表',
                        'context': '合作伙伴关联数据',
                        'cfp': 1
                    },
                    {
                        'description': '获取适用分润规则',
                        'move_type': 'R',
                        'data_group': '适用分润规则',
                        'data_attributes': '规则ID,收益类型,分成比例配置',
                        'context': '适用分润规则',
                        'cfp': 1
                    },
                    {
                        'description': '存储月度账单',
                        'move_type': 'W',
                        'data_group': '月度账单',
                        'data_attributes': '账单ID,伙伴ID,账单周期,分润总额,状态',
                        'context': '月度账单',
                        'cfp': 1
                    },
                    {
                        'description': '存储账单明细项',
                        'move_type': 'W',
                        'data_group': '账单明细项',
                        'data_attributes': '明细ID,账单ID,来源流水ID,毛利贡献,分润金额,交易描述',
                        'context': '账单明细项',
                        'cfp': 1
                    },
                    {
                        'description': '存储计算日志',
                        'move_type': 'W',
                        'data_group': '计算日志',
                        'data_attributes': '日志ID,计算周期,处理流水数,生成账单数,异常记录数',
                        'context': '计算日志',
                        'cfp': 1
                    },
                    {
                        'description': '传输计算完成通知',
                        'move_type': 'X',
                        'data_group': '计算完成通知',
                        'data_attributes': '计算状态,处理统计,异常报告',
                        'context': '计算完成通知',
                        'cfp': 1
                    }
                ]
            )

            # 合作伙伴分润门户 - 登录和看板
            self.add_functional_process(
                platform=platform,
                functional_user="推广大使",
                user_requirement="登录后看到个人分润收益概况",
                trigger_event="登录分润门户",
                process_name=f"{platform}加载分润看板",
                subprocesses=[
                    {
                        'description': '接收看板数据请求',
                        'move_type': 'E',
                        'data_group': '看板数据请求',
                        'data_attributes': '用户ID,伙伴ID,请求时间',
                        'context': '看板数据请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取累计总收益',
                        'move_type': 'R',
                        'data_group': '累计总收益',
                        'data_attributes': '伙伴ID,累计分润总额,已支付金额',
                        'context': '累计总收益',
                        'cfp': 1
                    },
                    {
                        'description': '获取上月结算数据',
                        'move_type': 'R',
                        'data_group': '上月结算数据',
                        'data_attributes': '账单周期,分润金额,支付状态',
                        'context': '上月结算数据',
                        'cfp': 1
                    },
                    {
                        'description': '获取待结算总额',
                        'move_type': 'R',
                        'data_group': '待结算总额',
                        'data_attributes': '待支付账单数,待支付总金额',
                        'context': '待结算总额',
                        'cfp': 1
                    },
                    {
                        'description': '获取近期收益趋势',
                        'move_type': 'R',
                        'data_group': '近期收益趋势',
                        'data_attributes': '月份,分润金额,趋势数据',
                        'context': '近期收益趋势',
                        'cfp': 1
                    },
                    {
                        'description': '传输看板视图数据',
                        'move_type': 'X',
                        'data_group': '看板视图数据',
                        'data_attributes': '收益卡片数据,趋势图表数据,统计汇总',
                        'context': '看板视图数据',
                        'cfp': 1
                    }
                ]
            )

            # 查看账单列表
            self.add_functional_process(
                platform=platform,
                functional_user="推广大使",
                user_requirement="查看历史月度账单",
                trigger_event="访问我的账单页面",
                process_name=f"{platform}查看账单列表",
                subprocesses=[
                    {
                        'description': '接收账单列表请求',
                        'move_type': 'E',
                        'data_group': '账单列表请求',
                        'data_attributes': '伙伴ID,筛选条件,分页参数',
                        'context': '账单列表请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取月度账单记录',
                        'move_type': 'R',
                        'data_group': '月度账单记录',
                        'data_attributes': '账单ID,账单周期,分润总额,状态,结算日期',
                        'context': '月度账单记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输账单列表视图',
                        'move_type': 'X',
                        'data_group': '账单列表视图',
                        'data_attributes': '账单列表数据,分页数据,筛选选项',
                        'context': '账单列表视图',
                        'cfp': 1
                    }
                ]
            )

            # 查看账单详情
            self.add_functional_process(
                platform=platform,
                functional_user="推广大使",
                user_requirement="查看具体账单的收益明细",
                trigger_event="点击查看详情按钮",
                process_name=f"{platform}查看账单详情",
                subprocesses=[
                    {
                        'description': '接收账单详情请求',
                        'move_type': 'E',
                        'data_group': '账单详情请求',
                        'data_attributes': '账单ID,伙伴ID,请求时间',
                        'context': '账单详情请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取账单基本数据',
                        'move_type': 'R',
                        'data_group': '账单基本数据',
                        'data_attributes': '账单周期,分润总额,状态,结算日期',
                        'context': '账单基本数据',
                        'cfp': 1
                    },
                    {
                        'description': '获取账单明细项',
                        'move_type': 'R',
                        'data_group': '账单明细项',
                        'data_attributes': '交易日期,业务描述,来源社区,毛利贡献,分成金额',
                        'context': '账单明细项',
                        'cfp': 1
                    },
                    {
                        'description': '传输账单详情视图',
                        'move_type': 'X',
                        'data_group': '账单详情视图',
                        'data_attributes': '账单概要,明细列表,导出功能',
                        'context': '账单详情视图',
                        'cfp': 1
                    }
                ]
            )

            # 导出账单明细
            self.add_functional_process(
                platform=platform,
                functional_user="推广大使",
                user_requirement="导出账单明细为Excel文件",
                trigger_event="点击导出明细按钮",
                process_name=f"{platform}导出账单明细",
                subprocesses=[
                    {
                        'description': '接收导出请求',
                        'move_type': 'E',
                        'data_group': '导出请求',
                        'data_attributes': '账单ID,导出格式,伙伴ID',
                        'context': '导出请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取完整明细数据',
                        'move_type': 'R',
                        'data_group': '完整明细数据',
                        'data_attributes': '所有明细项,账单汇总,伙伴基本数据',
                        'context': '完整明细数据',
                        'cfp': 1
                    },
                    {
                        'description': '存储导出文件',
                        'move_type': 'W',
                        'data_group': '导出文件',
                        'data_attributes': '文件路径,文件名,生成时间,文件大小',
                        'context': '导出文件',
                        'cfp': 1
                    },
                    {
                        'description': '传输文件下载链接',
                        'move_type': 'X',
                        'data_group': '文件下载链接',
                        'data_attributes': '下载URL,文件名,过期时间',
                        'context': '文件下载链接',
                        'cfp': 1
                    }
                ]
            )
