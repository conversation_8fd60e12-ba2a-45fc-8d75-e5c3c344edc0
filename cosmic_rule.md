
## **COSMIC简介**

COSMIC方法作为当今主流功能规模度量方法之一,在软件功能点的划分上更加灵活具体；  
对软件内部结构和实现依赖更小,可以将功能过程定义的更加细致，其中包含的功能点也更加具体和单一；  
有较强应对软件功能变化的能力，可以在软件开发生命周期的各阶段使用；  
从用户功能的视角入手，起源于客户可以理解的术语，简单易行。  
它不仅适合于信息系统的规模度量，还适合于实时系统和多层系统的规模度量，因而该方法在商业应用软件和实时软件有极好的适用性

## **术语**

- **客户需求**: 用户原始需求。来源于项目建议书和需求部门的需求
- **功能用户**: 一个（类）用户是软件块的功能性用户需求中数据的发送者或者预期的接收者。
- **功能用户需求**: 用户需求的子集。这些需求以任务和服务的形式描述软件做什么
- **触发事件**: 待度量软件的功能性用户需求中可识别的一个事件，此事件使得一个或多个软件功能用户产生一个或多个数据组，每个数据组随后被一个触发输入所移动。
  - 功能过程的触发事件可以是：人、软件、时钟
  - **人或软件触发**:
    比如公司接收到一个订单后（触发事件）：
    1. 可以是人来触发，输入该订单的各项信息作为输入；
    2. 也可以是一个处理订单的软件来触发，软件自动处理填写订单的各项信息作为输入。
     
  - **时钟信号触发**:
    比如每天18点时定时任务自动统计并发送当日日报功能：
    触发事件为：每日18时系统时钟触发

- **功能过程**: 
  1. 体现了待度量软件的功能性用户需求基本部件的一组数据移动，该功能处理在该FUR中是独一无二的，并能独立于该FUR的其他功能处理被定义。
  2. 一个功能处理可能只有一个触发输入。每个功能处理在接受到由其触发输入数据移动所移动的一个数据组后，开始进行处理。
  3. 一个功能处理的所有数据移动的集合是满足其FUR的触发输入所有可能的响应所需的集合。

- **子过程描述**: 
  1. 每个功能处理由一系列子过程组成。
  2. 一个子处理可以是一个数据移动或者数据运算。


- **数据移动类型**: COSMIC规定的四种数据移动类型。包括：输入（E）输出（X）读（R）写（W）
  1. 输入E：可以是人输入了某些数据然后作为输入；也可以是点击某按钮进入菜单这种触发性输入。也可以是时钟触发的定时任务作为输入
  2. 输出X：输出是指软件把数据传回给功能用户的操作
    (1)在屏幕上输出：“保存失败”、“年龄不能为负数”、“网络连接失败”等都不被识别为输出，不计算功能点；
    (2)如果在提示信息中包含了用户感兴趣的数据，则需要识别为输出，如：“你输入的金额大于了套餐上限100万”
 
    1个数据组输出在屏幕上，并以相同的格式打印出来，识别为1个输出.
    1个数据组输出在屏幕上，但是以显著不同的格式打印出来，识别为2个输出
    1个数据组以图形的方式输出在屏幕上，以数据表格的形式打印出来，识别为2个输出
  3. 读R：从一个持久存储介质中读取了数据。如果读取的数据组是多个且明显不同，可以识别为多个读操作。持久存储介质：可以是数据库、excel表格、txt文件等，不能是内存暂存缓存数据。
  4. 写W：把数据写入到一个持久存储介质中，或者在持久存储介质中修改删除数据。如果写入的数据组是多个且明显不同，可以识别为多个写操作。
    删除操作可以作为一个单独的写数据移动进行度量。
    持久存储介质：可以是数据库、excel表格、txt文件等，不能是内存暂存缓存数据。

- **数据组**: 一个唯一的、非空的、无序的数据属性的集合
  可以简单理解为某一数据属性的集合名。
  比如客户需求为查询所有的人员信息：
  数据属性为：姓名、手机号、邮箱、所在部门
  数据组可以命名为：人员基本信息
   
  比如客户需求为查询公司领导的信息：
  数据属性为：姓名、手机号、邮箱、所在部门
  数据组可以命名为：公司领导基本信息

- **数据属性**: 一个数据属性是一个已识别的数据组中最小的信息单元。
- **CFP**:  COSMIC 功能点，表示一个数据移动的规模。也叫子处理。
  Cosmic Function Point（COSMIC 功能点），简称CFP。被定义为一个数据移动规模。正常一行数据CFP为1，重复为0.33，无效为0。
- **持久存储介质**: 包括文件、数据库、网络、ROM、RAM

## **基本要求**

- 功能过程需要与需求文档最小章节一一对应
- 功能过程必须唯一
- 子过程,数据组,子过程+数据组尽量做到唯一(重复的内容容易被判为复用,甚至是被去重)
- 所有功能过程尽量控制在5个子过程左右,最小3个子过程(输入,读取,输出,写入),一般不要超过8个子过程,如果超过8个子过程,需要判断是否能拆分为2个功能过程
- 子过程必须由E开头,X或W结尾
- 数据移动类型+数据组必须唯一
- 为了满足上述唯一性的要求,描述时尽量把功能过程的对象名称加入描述中

## **基本规范**

- **功能用户需求**:分模块进行,不能完全一样
  - ❎错例: 整片文档都是定时出库程序
  - ✅对例: 一号出库程序, 二号出库程序...

- **功能过程**: 功能过程定义需要明确
  - ❎错例: 功能过程,连接数据库...

- **子过程描述**: 在子过程描述中不允许出现  “判断”、“如果”、“点击”、“提交” 等,子过程描述必须与数据移动类型一致

- **数据组**: 某一数据属性的集合名,在数据组中**不允许**出现 “XX程序”,建议使用 “XX文件”,“XX信息”
  - ❎错例: XX程序
  - ✅对例: XX文件, XX信息
  
- **数据属性**: 在数据属性中**不允许**出现"数据库", "XX信息", "XX配置"
- 文档中尽量不要出现**专业术语**,尽量使用中文描述

## **检查规则**(非常重要)

- **问题（一）**：功能过程第一个子过程描述的数据移动类型不是E；
  - 解释：每个功能过程的第一个子过程的数据移动类型应为E。

- **问题（二）**：功能过程未包含两个必要的数据移动类型（EW或EX）；
  - 解释：一个功能过程至少包括一个输入（E）数据移动，以及一个写（W）或输出（X）数据移动，即一个功能过程至少要包含两个必要的数据移动（EW或EX）。

- **问题（三）**：子过程描述、数据移动类型、数据组、数据属性有空值；
  - 解释：这四个属性不能为空，否则无法判断功能点的复用情况。

- **问题（四）**：功能过程存在重复行；
  - 解释：功能过程与其它行相同。

- **问题（五）**：子过程描述存在重复行；
  - 解释：子过程描述与其它行相同。

- **问题（六）**：数据移动类型、数据组、数据属性存在重复行；
  - 解释：数据移动类型、数据组、数据属性与其它行相同。

- **问题（七）**：数据移动类型、数据组存在重复行；
  - 解释：数据移动类型、数据组与其它行相同。

- **问题（八）**：数据移动类型、数据属性存在重复行；
  - 解释：数据移动类型、数据属性与其它行相同。

- **问题（九）**：CFP不为1或0.33；
  - 解释：CFP代表一个数据移动，CFP只能是1、0.33、0，其它数量不建议使用。

- **待确认问题（十）**：子过程描述相似度过高；
  - 解释：子过程描述与其它行相似度过高（默认高于85%），需人工确认是否存在重复。

- **待确认问题（十一）**：子过程描述中存在可能未跨层交互的关键词；
  - 解释：计算、组装、解析、验证等没有后续输入写之类的操作就是无效，建议改为计算xxx并输出xxx。可能未跨层交互的关键词：验证、计算、解码、解析、组装、校验、拼接、是否、判断、如果、点击、提交

- **待确认问题（十二）**：功能过程中存在非Cosmic类功能点的关键词；子过程描述中存在非Cosmic类功能点的关键词；
  - 解释：非Cosmic类功能点不应出现在Cosmic文档中，如果文档中涉及到非Cosmic关键词但是描述的的确不是非Cosmic功能点的话可以忽略。
  - 非Cosmic类功能点的关键词：调优、优化、配置、调研、测试、集成、部署、UI、UE、支撑、报告、割接、合法性校验、规则校验、输入校验、联调、设计、现场、实施


## **优化改造的功能过程建议**

对于增加字段或增加选项的,设计的所有场景(新增,修改,审核,列表,详情,导出,消息接口)都可单独列一个功能过程,但提供的需求文档中,涉及的所有场景都需要列出来

## **COSMIC拆分注意事项**

- 子过程描述需要注意描述语言，最好能让评审人员看出有跨层的数据移动，否则会被视为无效（这也是扣减最多的地方）
- 输入的功能用户可以是人、其他组件、其他系统、设备，但是操作系统不认为是功能用户，所以所有操作系统的操作不能当成一个输入，比如界面从系统时钟获取时间。
- 一个录入界面，输入2个字段和输入100个字段，都只能识别为一个输入。界面的联动、JS校验，都不能识别为功能点
- 界面静态数据或者出现再屏幕上的应用程序的常规信息，不能识别为功能点。比如标题、脚注、固定的菜单
- 控制命令不能识别为功能点，比如翻页
- 一个页面有时间顺序的输入也只能识别为一个功能点。比如：多个下拉框都需要后台读取数据，如果在一个功能点，只能识别为一个输入
- 一个功能处理中所有的错误、正确确认消息，只能合并为一个输出。
- 操作系统发布的错误信息，不识别为功能点。比如提示系统错误；报404等。
- 同一个功能处理中，错误信息含有变量的，按照变量的类别识别为多个。比如：张三的身份证有误、李四的身份证有误、二号摄像头出错，识别为2个输出。 
- 定时任务触发，不能直接作为输入，要写具体的输入信息，比如：商品催办时间到。
- 在子过程描述中不允许出现  “判断”、“如果”、“点击”、“提交” 等。
- 在数据组中不允许出现 “XX程序”，建议使用：“XX信息”"XX文件"。
- 在数据属性中不允许出现 “数据库”“XX信息”“XX配置”。
- 在文档中尽量少出现专业术语，可以使用中文进行翻译的建议使用中文，保证是用户可以看的懂的文字描述。

- 子过程不能拆“读取XX模板”
- 报表导出导入的功能不要单独报需求，要尽量跟其他需求放一起；
- 不要写“生成XX数据”，可以写成“保存XX信息并生成文件”；
- “返回XX反馈信息”这种不被认可；
- “用户删除XX”和“校验XX”都不被认可；

## **COSMIC评估过程**

- 同一功能过程含有多个子过程但仅有一种数据移动（说明为笔误，包含EX或EW，E开头，一般只有一个E，数据移动类型描述和字母对应，建议输入、读取、保存、同步、展示等关键词）
- 功能重复：同一功能过程或不同功能过程中子过程描述、数据移动、数据组、数据属性完全一致（子过程描述、数据移动、数据组（数据属性），两个一致，0.33，三个一致，0，其它情况为1，改成不一样）
- 同一功能过程仅含有一个子过程（说明为笔误，必须有多个子过程，功能过程合并）
- 非规则性校验：如输入合法性校验（描述问题，与厂家核实理解不为规范中的意思，为设计规则校验，不能含合法性、校验等，建议改成读取XX文件进行校验）
- 非功能性功能点：如“调优”、“优化”、“配置”等（经与厂家核实，选择使用开放式处理调优、优化、配置等，建议改成实现自动配置功能。）
- 非开发阶段功能点（经与厂家核实，字面意思非技术意思，表达方式不一致。说明为笔误，不能含设计、调研、测试、集成、部署、UI、UE、支撑、报告、割接等文字）
- 验证、计算、解码、解析、组装、校验、拼接、是否、判断、如果、点击、提交   属于未跨界的，判断为利旧。计算、组装、解析、验证没有后续输入写之类的操作就是无效，建议改为计算xxx并输出xxx
- 功能数据移动类型保持一致。
- 关键词匹配：调优、优化、配置、调研、测试、集成、部署、UI、UE、支撑、报告、割接、合法性校验、规则校验、输入校验、联调、设计、现场、实施
- 非功能需求：界面设计、UI、UE、脚本配置、数据库操作
- 功能需求，适合拆分，拆分表可以填
- 重新开发新增、复用0.33、现有利旧，没有功能点

## **COSMIC交流纪要**

- 有工具检查是否重复，并会把历史提交的cosmic入库形成数据字典，作功能子过程的重复性检查
- 子过程内容尽量详尽，多加定语，把修改内容体现出来，避免和其他的子过程重复
- 子过程+数据移动类型+数据组及属性完全一致，判断为重复
- 校验、计算这些不要单独作为子过程，可以拆为读取规则、数据处理、输出结果这样子，能够对应到输入输出、读和写
- 仅仅是计算、解析、解码、组装、验证、校验、拼接、判断这些，没有后续的输入 、写之类的操作都会判定为未跨层跨界，但如果描述是计算XXX，并输出XXX，就为有效功能点
- 功能架构图尽可能完善，二级模块对应到表格中的功能用户需求 
- 对原有功能的优化改造，从发文到需求内容，尽量表述新增的内容，比如新增XXX环节、新增XXX字段，对应的功能过程判定为新增的概率会高些
- 比较复杂的页面或报表，涉及多个读、输出的，拆成多个功能过程 ，但相应的需求文档中要体现复杂度，最好每个输出单独写成需求小节
- 从需求文档到功能过程，还是尽量的细化，比如一个新增可以拆成多个功能过程
- 根据功能点核算工作量的各项系数是省公司计划部定的，厂商不得调整，建议提交的送审材料，还是要尽量多拆

