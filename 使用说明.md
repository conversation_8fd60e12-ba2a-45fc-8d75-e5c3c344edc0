# COSMIC功能点生成器使用说明

## 快速开始

### 方法一：使用批处理文件（推荐）

1. **双击运行** `cosmic_menu.bat` 文件
2. **选择操作**：
   - `1` - 生成COSMIC功能点
   - `2` - 检查功能点规范
   - `3` - 转换为Excel格式
   - `4` - 生成统计报告
   - `5` - 退出

### 方法二：使用命令行

```bash
# 查看可用生成器
python base_script_files\generate_cosmic.py --list

# 交互式生成
python base_script_files\generate_cosmic.py --interactive

# 生成特定类型
python base_script_files\generate_cosmic.py --type cmcc_wallet
python base_script_files\generate_cosmic.py --type third_party_mall

# 生成所有类型
python base_script_files\generate_cosmic.py --all
```

## 详细操作流程

### 1. 生成COSMIC功能点

**批处理方式：**
1. 选择菜单选项 `1`
2. 系统显示可用生成器列表
3. 输入数字选择要生成的系统（可输入多个，用逗号分隔）
4. 系统自动生成CSV文件

**命令行方式：**
```bash
python base_script_files\generate_cosmic.py --interactive
```

**可用的生成器：**
- `cmcc_wallet` - 中国移动和包支付集成系统
- `third_party_mall` - 第三方商城积分消费集成系统

### 2. 检查功能点规范

**批处理方式：**
1. 选择菜单选项 `2`
2. 系统显示可用CSV文件列表（带序号）
3. 输入文件序号选择要检查的文件
4. 查看检查结果

**命令行方式：**
```bash
python base_script_files\cosmic_checker.py --file cosmic_results\filename.csv
```

**检查内容：**
- 必填字段完整性
- 数据移动类型正确性
- 功能过程逻辑完整性
- 数据属性唯一性
- 禁用关键词检查

### 3. 转换为Excel格式

**批处理方式：**
1. 选择菜单选项 `3`
2. 从列表中选择要转换的CSV文件
3. 系统生成对应的Excel文件

**命令行方式：**
```bash
python base_script_files\csv_to_xlsx_merger.py --input cosmic_results\filename.csv
```

**Excel文件特性：**
- 自动合并相同内容的单元格
- 专业的表格样式
- 适合报告和文档提交

### 4. 生成统计报告

**批处理方式：**
1. 选择菜单选项 `4`
2. 选择要统计的CSV文件
3. 查看详细统计信息

**命令行方式：**
```bash
python base_script_files\cosmic_stats.py cosmic_results\filename.csv
```

**统计内容：**
- 按平台分布统计
- 按功能用户统计
- 按数据移动类型统计
- CFP值汇总

## 文件说明

### 输入文件
- `design_doc_files/*.md` - 设计文档（Markdown格式）

### 输出文件
- `cosmic_results/*_cosmic_points.csv` - COSMIC功能点CSV文件
- `cosmic_results/*_cosmic_points.xlsx` - Excel格式文件
- `*_analysis.json` - 设计文档分析结果

### 配置文件
- `cosmic_rule.md` - COSMIC规则文档
- `design_doc_generator/` - 生成器模块目录
- `base_script_files/` - 基础脚本文件目录

## 高级功能

### 分析设计文档生成新的生成器

```bash
python base_script_files\generate_cosmic.py --analyze-doc design_doc_files\design_document.md
```

这个功能可以：
1. 自动分析Markdown格式的设计文档
2. 识别功能模块和业务流程
3. 生成对应的COSMIC功能点生成器
4. 保存分析结果为JSON文件

### 批量操作

```bash
# 生成所有类型的功能点
python base_script_files\generate_cosmic.py --all

# 生成多个特定类型
python base_script_files\generate_cosmic.py --types "cmcc_wallet,third_party_mall"
```

## 故障排除

### 常见问题

1. **"找不到Python命令"**
   - 确保Python已正确安装并添加到PATH环境变量

2. **"模块导入错误"**
   - 安装必要的依赖包：`pip install pandas openpyxl`

3. **"文件不存在"**
   - 确保在正确的目录下运行脚本
   - 检查文件名是否正确

4. **"编码错误"**
   - 确保CSV文件使用UTF-8编码

### 技术支持

如果遇到其他问题：
1. 检查Python版本（建议3.6+）
2. 确认所有依赖包已安装
3. 查看错误信息并检查文件路径
4. 确保有足够的磁盘空间

## 项目结构

```
cosmic_check/
├── cosmic_menu.bat                        # 主菜单批处理文件
├── base_script_files/                     # 基础脚本文件目录
│   ├── generate_cosmic.py                     # 统一生成接口
│   ├── cosmic_checker.py                      # 规范检查器
│   ├── csv_to_xlsx_merger.py                  # Excel转换器
│   └── cosmic_stats.py                        # 统计报告生成器
├── design_doc_generator/                  # 生成器模块目录
│   ├── base_cosmic_generator.py               # 基础生成器类
│   ├── cosmic_generator_factory.py            # 生成器工厂
│   ├── design_doc_analyzer.py                 # 设计文档分析器
│   ├── cmcc_wallet_generator.py               # 和包支付生成器
│   └── third_party_mall_generator.py          # 第三方商城生成器
├── cosmic_results/                        # 结果文件目录
│   ├── *_cosmic_points.csv                    # 生成的CSV文件
│   └── *_cosmic_points.xlsx                   # 转换的Excel文件
├── design_doc_files/                      # 设计文档目录
│   └── *.md                                   # 用户提供的设计文档
├── cosmic_rule.md                         # COSMIC规则文档
└── 使用说明.md                           # 本文档
```

## 版本信息

- 版本：v1.0
- 更新日期：2025-06-27
- 支持的COSMIC版本：4.0.2
