## 【V5.0 增强】**9. 适老化改造与无障碍服务 (AG - Aging-friendly) **
**版本**: 1.0 **模块归属**: 平台核心增值业务 **关联模块**: 资质审批中心(ASC), 服务商管理(SPO), 平台收益中心(RC), 第三方生态集成(TE), 广告管理(AM)

#### **1. 模块概述**

##### **1.1. 目标**

本模块旨在解决用户（特别是长者子女）在面对居家养老改造时“**不知改什么、不知买什么、不知找谁装**”的核心痛点。通过打造“**内容引领→方案推荐→产品采购→服务落地**”的一站式、闭环服务，构建平台最高价值的收入来源，并真正为用户提供可信赖的居家安全解决方案。

##### **1.2. 核心用户角色**

|角色|所属端|核心职责|
|---|---|---|
|**长者子女**|移动App|**核心目标用户**。发起安全评估、浏览改造方案、决策并购买产品/服务、监督服务进程。|
|**老年居民**|移动App/硬件|服务的最终受益者，可能会在子女协助下参与评估或直接通过电话咨询。|
|**平台运营管理员**|Web后台|配置管理改造方案套餐、审核服务商资质、分配服务订单。|
|**适老化改造服务商**|Web后台/商家App|提供专业的改造方案设计、施工服务，是平台服务履约的核心。|

##### **1.3. 核心数据实体 (Data Models)**

- **`AgingSafetyAssessment` (居家安全评估记录)**:
    - `assessment_id` (主键)
    - `user_id` (发起评估的用户ID)
    - `resident_id` (关联的居住长者档案ID)
    - `submission_data` (JSONB: 存储问卷的每个问题与答案)
    - `risk_score` (整型: 系统根据答案计算的风险分值)
    - `generated_report` (TEXT: 系统生成的文字版风险报告)
- **`RenovationSolutionPackage` (改造方案套餐)**:
    - `package_id` (主键)
    - `package_name` (套餐名称, e.g., "浴室防滑安全套餐")
    - `description` (富文本描述), `cover_image` (封面图)
    - `target_scene` (适用场景, 枚举: 'BATHROOM', 'BEDROOM', 'KITCHEN'...)
    - `estimated_price` (预估总价)
    - `included_items` (JSONB: 包含的产品和服务清单，含SKU、名称、数量)
- **`RenovationProject` (改造项目单)**:
    - `project_id` (主键)
    - `user_id` (下单用户)
    - `address` (服务地址)
    - `status` (枚举: `待指派`, `待报价`, `待用户确认`, `施工中`, `待验收`, `已完成`, `已取消`)
    - `source_type` (来源类型: '来自评估报告', '来自套餐选购', '用户自定义')
    - `assigned_provider_id` (关联承接服务的改造服务商ID)
    - `final_quote_amount` (服务商最终报价)

---

#### **2. 详细功能规格**

##### **2.1. AG-APP: 移动端核心流程**

- **FR-AG-APP-001: 居家安全自测**
    - **用户故事**: 作为担心父母居家安全的子女，我希望通过一个简单的问卷，就能快速了解家里的安全隐患。
    - **界面**: 一个交互式的问卷向导，以图文并茂的形式呈现。
        - **分组**: 问题按场景分组（如“浴室环境”、“卧室环境”、“厨房安全”）。
        - **问题形式**: “您家中浴室地面是否有防滑措施？ [是/否]”、“床边是否有方便起夜的扶手？ [是/否]”。
    - **后端逻辑**:
        1. 用户提交问卷后，后端根据预设的风险权重矩阵，计算`risk_score`。
        2. 根据用户的回答，动态生成一份`generated_report`。例如，若用户选择“浴室地面无防滑措施”，报告中自动加入“高风险：浴室地面湿滑极易导致摔倒，建议加装防滑垫或防滑瓷砖。”
        3. 评估记录`AgingSafetyAssessment`存入数据库。
- **FR-AG-APP-002: 方案推荐与产品/服务浏览**
    - **用户故事**: 拿到风险报告后，我希望能直接看到平台推荐的、能解决这些问题的具体产品或服务套餐。
    - **界面**:
        1. **评估结果页**: 在展示风险报告的同时，下方会根据报告内容，智能推荐1-2个最相关的`RenovationSolutionPackage`。
        2. **“居家改造”主页**: App内独立的“居家改造”一级入口。
            - **广告位**: 顶部为**广告推广位 (FR-AG-AD-001)**，展示平台主推的改造套餐或认证服务商。
            - **方案超市**: 以卡片形式陈列所有标准化的**改造方案套餐**，用户可按场景筛选浏览。
    - **套餐详情页**:
        - 展示套餐的详细图文介绍、预估价格、包含的产品/服务列表、用户评价。
        - 提供两个明确的操作按钮：“**购买套餐内产品**”（跳转至商城）和“**预约整体改造服务**”（创建改造项目单）。
- **FR-AG-APP-003: 第三方商城(卓望)产品采购**
    - **用户故事**: 我看中了套餐里的一个扶手，希望能无缝跳转到卓望商城完成购买，并能用我的社区积分抵扣一部分钱。
    - **流程**:
        1. 用户在App内点击某个产品，通过**SSO(TE-001)**无缝跳转至内嵌的卓望商城H5商品页。
        2. 用户在商城完成下单支付流程。
        3. **【技术关键点】支付环节**: 卓望商城需调用我方平台接口，查询用户可用积分，并在其支付页面展示积分抵扣选项。
        4. **【技术关键点】购买成功回调**: 用户支付成功后，卓望商城**必须**通过服务端API回调我方平台，通知此笔订单的详细信息（`user_id`, `order_id`, `GMV`, `使用的积分数`等）。
        5. 我方平台接收到回调后，自动在**收益中心(RC)**记录一笔`source_type='MALL_SHARE'`的佣金流水，并扣除用户相应积分。
- **FR-AG-APP-004: 预约本地服务与项目追踪**
    - **用户故事**: 我买好了产品，或者想直接找人全包改造，我希望能在平台一键预约一个靠谱的本地师傅，并随时了解项目进展。
    - **流程**:
        1. **发起预约**: 用户点击“预约整体改造服务”后，系统创建一个`RenovationProject`，状态为`待指派`。
        2. **平台指派**: 运营管理员在后台，为该项目指派一个已通过**资质审批(ASC)**的本地服务商。
        3. **服务商报价**: 服务商（可能需要上门勘测后）在其商家端App提交详细的施工报价。
        4. **用户确认**: 用户在App收到报价通知，可选择“接受报价”或“拒绝”。接受后，项目状态变为`施工中`。
        5. **进度追踪**: 用户可在“我的改造项目”中，查看项目的实时状态。
        6. **完工验收**: 服务商在商家端标记“施工完成”，项目状态变为`待验收`。用户需在App内点击“**确认完工**”按钮进行最终验收。
        7. **触发结算**: 用户点击“确认完工”是关键节点。此操作将触发支付流程（如有尾款），并同时在**收益中心(RC)**记录此项目的平台佣金流水。项目状态变为`已完成`。
    - **评价**: 项目完成后，用户可以对服务商进行星级评价和文字评论。

##### **2.2. AG-WEB: 后台管理功能**

- **FR-AG-WEB-001: 改造方案套餐管理**
    - **用户故事**: 作为平台运营，我需要一个后台功能，可以灵活地创建和组合不同的改造方案套餐，以适应市场需求。
    - **界面**: 一个可视化的套餐配置后台。
    - **功能**:
        - 创建/编辑套餐：填写名称、描述、上传封面图、设定预估价格。
        - 管理套餐内容：可以从已集成的卓望商城产品库中选择商品，或从平台的服务目录中选择服务，作为套餐的组成部分，并定义数量。
- **FR-AG-WEB-002: 改造项目单管理**
    - **用户故事**: 作为平台运营，我需要看到所有的改造项目单，并能手动为它们指派最合适的服务商。
    - **界面**: 改造项目单的列表页，类似工单系统。
    - **功能**:
        - 查看所有项目单的详情和状态。
        - 为`待指派`的项目单，从已认证的服务商列表中选择并指派服务商。
        - 监督所有项目的进度，处理超时或异常的项目。
- **FR-AG-WEB-003: 广告推广配置**
    - **用户故事**: 我想把“浴室防滑安全套餐”作为本月主推，需要把它配置到App首页的广告位上。
    - **界面**: 关联**广告管理(AM)**模块，提供一个专门的“适老化改造推广”配置区。
    - **功能**: 运营可以选择一个`RenovationSolutionPackage`或一个认证的`服务商`，并将其关联到App内预设的、ID为`AG_HOMEPAGE_BANNER`等专属广告位上，并设置推广起止时间。

#### **3. 非功能性需求**

- **信任与安全**: 所有上线服务的“适老化改造服务商”**必须**通过“资质与服务审批中心(ASC)”的严格审核。服务商的服务历史、用户评价必须在App端对用户公开透明。
- **集成可靠性**: 与卓望商城的回调接口必须设计重试和补偿机制，确保在网络波动等情况下，订单和佣金数据不会丢失。