#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
适老化改造与无障碍服务 COSMIC功能点生成器
基于设计文档生成符合COSMIC规范的功能点
"""

import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class AgingFriendlyServiceGenerator(BaseCosmicGenerator):
    """适老化改造与无障碍服务COSMIC功能点生成器"""
    
    def __init__(self):
        super().__init__(
            subsystem="智慧社区",
            module_level2="适老化改造与无障碍服务", 
            module_level3="移动端核心流程"
        )
    
    def generate_functional_processes(self):
        """生成适老化改造与无障碍服务的所有功能过程"""
        
        platforms = ["PC端", "移动端"]
        
        for platform in platforms:
            # 2.1. AG-APP: 移动端核心流程
            # FR-AG-APP-001: 居家安全自测
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="通过简单问卷快速了解家里的安全隐患",
                trigger_event="用户访问居家安全自测功能",
                process_name=f"{platform}执行居家安全自测",
                subprocesses=[
                    {
                        'description': '接收安全评估请求',
                        'move_type': 'E',
                        'data_group': '安全评估请求',
                        'data_attributes': '用户ID,居住长者档案ID,评估类型',
                        'context': '安全评估请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取评估问卷模板',
                        'move_type': 'R',
                        'data_group': '评估问卷模板',
                        'data_attributes': '问题ID,问题内容,场景分组,风险权重',
                        'context': '评估问卷模板',
                        'cfp': 1
                    },
                    {
                        'description': '传输问卷界面',
                        'move_type': 'X',
                        'data_group': '问卷界面',
                        'data_attributes': '问题列表,选项内容,图文说明',
                        'context': '问卷界面',
                        'cfp': 1
                    }
                ]
            )
            
            # 提交评估问卷
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="提交安全评估问卷并获得风险报告",
                trigger_event="用户完成问卷并提交",
                process_name=f"{platform}生成安全评估报告",
                subprocesses=[
                    {
                        'description': '接收问卷答案',
                        'move_type': 'E',
                        'data_group': '问卷答案',
                        'data_attributes': '用户ID,问题答案,提交时间',
                        'context': '问卷答案',
                        'cfp': 1
                    },
                    {
                        'description': '获取风险权重矩阵',
                        'move_type': 'R',
                        'data_group': '风险权重矩阵',
                        'data_attributes': '问题ID,答案选项,风险分值,权重系数',
                        'context': '风险权重矩阵',
                        'cfp': 1
                    },
                    {
                        'description': '获取报告生成规则',
                        'move_type': 'R',
                        'data_group': '报告生成规则',
                        'data_attributes': '风险场景,建议内容,推荐方案',
                        'context': '报告生成规则',
                        'cfp': 1
                    },
                    {
                        'description': '存储评估记录',
                        'move_type': 'W',
                        'data_group': '评估记录',
                        'data_attributes': '评估ID,风险分值,生成报告,评估时间',
                        'context': '评估记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输评估结果',
                        'move_type': 'X',
                        'data_group': '评估结果',
                        'data_attributes': '风险报告,风险分值,改进建议',
                        'context': '评估结果',
                        'cfp': 1
                    }
                ]
            )
            
            # FR-AG-APP-002: 方案推荐与产品/服务浏览
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="根据评估结果查看推荐的改造方案套餐",
                trigger_event="用户浏览改造方案推荐",
                process_name=f"{platform}浏览改造方案套餐",
                subprocesses=[
                    {
                        'description': '接收方案浏览请求',
                        'move_type': 'E',
                        'data_group': '方案浏览请求',
                        'data_attributes': '用户ID,评估ID,场景筛选,推荐类型',
                        'context': '方案浏览请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户评估结果',
                        'move_type': 'R',
                        'data_group': '用户评估结果',
                        'data_attributes': '评估ID,风险分值,风险场景,推荐标签',
                        'context': '用户评估结果',
                        'cfp': 1
                    },
                    {
                        'description': '获取改造方案套餐',
                        'move_type': 'R',
                        'data_group': '改造方案套餐',
                        'data_attributes': '套餐ID,套餐名称,适用场景,预估价格,包含项目',
                        'context': '改造方案套餐',
                        'cfp': 1
                    },
                    {
                        'description': '获取广告推广内容',
                        'move_type': 'R',
                        'data_group': '广告推广内容',
                        'data_attributes': '广告位ID,推广套餐,推广服务商,展示时间',
                        'context': '广告推广内容',
                        'cfp': 1
                    },
                    {
                        'description': '传输方案推荐页面',
                        'move_type': 'X',
                        'data_group': '方案推荐页面',
                        'data_attributes': '推荐套餐,广告内容,套餐详情,操作按钮',
                        'context': '方案推荐页面',
                        'cfp': 1
                    }
                ]
            )
            
            # 查看套餐详情
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="查看改造套餐的详细内容和价格",
                trigger_event="用户点击套餐查看详情",
                process_name=f"{platform}查看套餐详情",
                subprocesses=[
                    {
                        'description': '接收套餐详情请求',
                        'move_type': 'E',
                        'data_group': '套餐详情请求',
                        'data_attributes': '套餐ID,用户ID,来源页面',
                        'context': '套餐详情请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取套餐完整内容',
                        'move_type': 'R',
                        'data_group': '套餐完整内容',
                        'data_attributes': '套餐描述,封面图片,产品清单,服务清单,价格明细',
                        'context': '套餐完整内容',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户评价',
                        'move_type': 'R',
                        'data_group': '用户评价',
                        'data_attributes': '评价ID,用户评分,评价内容,评价时间',
                        'context': '用户评价',
                        'cfp': 1
                    },
                    {
                        'description': '传输套餐详情页面',
                        'move_type': 'X',
                        'data_group': '套餐详情页面',
                        'data_attributes': '套餐介绍,产品列表,服务说明,用户评价,购买选项',
                        'context': '套餐详情页面',
                        'cfp': 1
                    }
                ]
            )

            # FR-AG-APP-003: 第三方商城产品采购
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="跳转到卓望商城购买套餐内产品",
                trigger_event="用户点击购买套餐内产品",
                process_name=f"{platform}跳转商城购买产品",
                subprocesses=[
                    {
                        'description': '接收商城跳转请求',
                        'move_type': 'E',
                        'data_group': '商城跳转请求',
                        'data_attributes': '用户ID,产品SKU,套餐ID,跳转来源',
                        'context': '商城跳转请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户SSO认证',
                        'move_type': 'R',
                        'data_group': '用户SSO认证',
                        'data_attributes': '用户ID,认证令牌,权限范围,有效期',
                        'context': '用户SSO认证',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户积分余额',
                        'move_type': 'R',
                        'data_group': '用户积分余额',
                        'data_attributes': '用户ID,可用积分,积分等级,使用规则',
                        'context': '用户积分余额',
                        'cfp': 1
                    },
                    {
                        'description': '传输商城跳转链接',
                        'move_type': 'X',
                        'data_group': '商城跳转链接',
                        'data_attributes': 'SSO令牌,商品链接,积分参数,回调地址',
                        'context': '商城跳转链接',
                        'cfp': 1
                    }
                ]
            )

            # 处理商城购买回调
            self.add_functional_process(
                platform=platform,
                functional_user="系统",
                user_requirement="处理卓望商城的购买成功回调",
                trigger_event="卓望商城回调购买成功通知",
                process_name=f"{platform}处理商城购买回调",
                subprocesses=[
                    {
                        'description': '接收商城回调通知',
                        'move_type': 'E',
                        'data_group': '商城回调通知',
                        'data_attributes': '订单ID,用户ID,商品明细,支付金额,使用积分',
                        'context': '商城回调通知',
                        'cfp': 1
                    },
                    {
                        'description': '获取佣金计算规则',
                        'move_type': 'R',
                        'data_group': '佣金计算规则',
                        'data_attributes': '商品类别,佣金比例,计算方式,结算周期',
                        'context': '佣金计算规则',
                        'cfp': 1
                    },
                    {
                        'description': '修改用户积分余额',
                        'move_type': 'W',
                        'data_group': '用户积分余额',
                        'data_attributes': '用户ID,扣减积分,余额更新,变动记录',
                        'context': '用户积分余额',
                        'cfp': 1
                    },
                    {
                        'description': '存储收益流水记录',
                        'move_type': 'W',
                        'data_group': '收益流水记录',
                        'data_attributes': '流水ID,来源订单,收益类型,佣金金额,社区归属',
                        'context': '收益流水记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输处理结果确认',
                        'move_type': 'X',
                        'data_group': '处理结果确认',
                        'data_attributes': '处理状态,积分扣减,收益记录,确认时间',
                        'context': '处理结果确认',
                        'cfp': 1
                    }
                ]
            )

            # FR-AG-APP-004: 预约本地服务与项目追踪
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="预约整体改造服务并创建项目单",
                trigger_event="用户点击预约整体改造服务",
                process_name=f"{platform}创建改造项目",
                subprocesses=[
                    {
                        'description': '接收服务预约请求',
                        'move_type': 'E',
                        'data_group': '服务预约请求',
                        'data_attributes': '用户ID,套餐ID,服务地址,联系方式,特殊要求',
                        'context': '服务预约请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户地址验证',
                        'move_type': 'R',
                        'data_group': '用户地址验证',
                        'data_attributes': '地址详情,社区归属,服务范围,可达性',
                        'context': '用户地址验证',
                        'cfp': 1
                    },
                    {
                        'description': '存储改造项目单',
                        'move_type': 'W',
                        'data_group': '改造项目单',
                        'data_attributes': '项目ID,用户ID,项目状态,来源类型,创建时间',
                        'context': '改造项目单',
                        'cfp': 1
                    },
                    {
                        'description': '传输项目创建结果',
                        'move_type': 'X',
                        'data_group': '项目创建结果',
                        'data_attributes': '项目ID,项目状态,后续流程,联系方式',
                        'context': '项目创建结果',
                        'cfp': 1
                    }
                ]
            )

            # 项目进度追踪
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="查看改造项目的实时状态和进度",
                trigger_event="用户访问我的改造项目",
                process_name=f"{platform}追踪项目进度",
                subprocesses=[
                    {
                        'description': '接收项目查询请求',
                        'move_type': 'E',
                        'data_group': '项目查询请求',
                        'data_attributes': '用户ID,项目ID,查询类型',
                        'context': '项目查询请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取项目状态记录',
                        'move_type': 'R',
                        'data_group': '项目状态记录',
                        'data_attributes': '项目ID,当前状态,服务商,报价金额,进度节点',
                        'context': '项目状态记录',
                        'cfp': 1
                    },
                    {
                        'description': '获取项目操作历史',
                        'move_type': 'R',
                        'data_group': '项目操作历史',
                        'data_attributes': '操作时间,操作类型,操作人,状态变更,备注说明',
                        'context': '项目操作历史',
                        'cfp': 1
                    },
                    {
                        'description': '传输项目进度页面',
                        'move_type': 'X',
                        'data_group': '项目进度页面',
                        'data_attributes': '项目状态,进度时间线,操作按钮,联系方式',
                        'context': '项目进度页面',
                        'cfp': 1
                    }
                ]
            )

            # 项目完工验收
            self.add_functional_process(
                platform=platform,
                functional_user="长者子女",
                user_requirement="确认改造项目完工并进行验收",
                trigger_event="用户点击确认完工按钮",
                process_name=f"{platform}确认项目完工",
                subprocesses=[
                    {
                        'description': '接收完工确认请求',
                        'move_type': 'E',
                        'data_group': '完工确认请求',
                        'data_attributes': '项目ID,用户ID,验收结果,评价内容',
                        'context': '完工确认请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取项目结算规则',
                        'move_type': 'R',
                        'data_group': '项目结算规则',
                        'data_attributes': '项目类型,佣金比例,结算方式,平台费率',
                        'context': '项目结算规则',
                        'cfp': 1
                    },
                    {
                        'description': '修改项目完成状态',
                        'move_type': 'W',
                        'data_group': '项目完成状态',
                        'data_attributes': '项目ID,完成状态,验收时间,用户评价',
                        'context': '项目完成状态',
                        'cfp': 1
                    },
                    {
                        'description': '存储平台佣金流水',
                        'move_type': 'W',
                        'data_group': '平台佣金流水',
                        'data_attributes': '流水ID,项目ID,佣金金额,收益类型,结算状态',
                        'context': '平台佣金流水',
                        'cfp': 1
                    },
                    {
                        'description': '传输完工确认结果',
                        'move_type': 'X',
                        'data_group': '完工确认结果',
                        'data_attributes': '确认状态,结算金额,评价提交,后续服务',
                        'context': '完工确认结果',
                        'cfp': 1
                    }
                ]
            )

            # 2.2. AG-WEB: 后台管理功能
            # FR-AG-WEB-001: 改造方案套餐管理
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="创建和管理改造方案套餐",
                trigger_event="管理员访问套餐管理后台",
                process_name=f"{platform}管理改造方案套餐",
                subprocesses=[
                    {
                        'description': '接收套餐管理请求',
                        'move_type': 'E',
                        'data_group': '套餐管理请求',
                        'data_attributes': '管理员ID,操作类型,套餐ID',
                        'context': '套餐管理请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取套餐列表',
                        'move_type': 'R',
                        'data_group': '套餐列表',
                        'data_attributes': '套餐ID,套餐名称,适用场景,状态,创建时间',
                        'context': '套餐列表',
                        'cfp': 1
                    },
                    {
                        'description': '获取商城产品库',
                        'move_type': 'R',
                        'data_group': '商城产品库',
                        'data_attributes': '产品SKU,产品名称,价格,分类,库存状态',
                        'context': '商城产品库',
                        'cfp': 1
                    },
                    {
                        'description': '获取平台服务目录',
                        'move_type': 'R',
                        'data_group': '平台服务目录',
                        'data_attributes': '服务ID,服务名称,服务类型,价格范围,服务商',
                        'context': '平台服务目录',
                        'cfp': 1
                    },
                    {
                        'description': '传输套餐管理界面',
                        'move_type': 'X',
                        'data_group': '套餐管理界面',
                        'data_attributes': '套餐列表,产品选择器,服务选择器,编辑工具',
                        'context': '套餐管理界面',
                        'cfp': 1
                    }
                ]
            )

            # 创建/编辑套餐
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="创建或编辑改造方案套餐内容",
                trigger_event="管理员提交套餐编辑表单",
                process_name=f"{platform}编辑套餐内容",
                subprocesses=[
                    {
                        'description': '接收套餐编辑请求',
                        'move_type': 'E',
                        'data_group': '套餐编辑请求',
                        'data_attributes': '套餐ID,套餐名称,描述内容,封面图片,预估价格,包含项目',
                        'context': '套餐编辑请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取套餐验证规则',
                        'move_type': 'R',
                        'data_group': '套餐验证规则',
                        'data_attributes': '必填字段,价格范围,产品限制,服务限制',
                        'context': '套餐验证规则',
                        'cfp': 1
                    },
                    {
                        'description': '存储套餐内容',
                        'move_type': 'W',
                        'data_group': '套餐内容',
                        'data_attributes': '套餐ID,套餐详情,包含产品,包含服务,更新时间',
                        'context': '套餐内容',
                        'cfp': 1
                    },
                    {
                        'description': '传输编辑结果',
                        'move_type': 'X',
                        'data_group': '编辑结果',
                        'data_attributes': '保存状态,套餐ID,验证结果,发布状态',
                        'context': '编辑结果',
                        'cfp': 1
                    }
                ]
            )

            # FR-AG-WEB-002: 改造项目单管理
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="管理所有改造项目单并指派服务商",
                trigger_event="管理员访问项目单管理页面",
                process_name=f"{platform}管理改造项目单",
                subprocesses=[
                    {
                        'description': '接收项目管理请求',
                        'move_type': 'E',
                        'data_group': '项目管理请求',
                        'data_attributes': '管理员ID,筛选条件,排序方式',
                        'context': '项目管理请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取项目单列表',
                        'move_type': 'R',
                        'data_group': '项目单列表',
                        'data_attributes': '项目ID,用户姓名,项目状态,创建时间,服务地址',
                        'context': '项目单列表',
                        'cfp': 1
                    },
                    {
                        'description': '获取认证服务商列表',
                        'move_type': 'R',
                        'data_group': '认证服务商列表',
                        'data_attributes': '服务商ID,服务商名称,服务范围,认证状态,评分',
                        'context': '认证服务商列表',
                        'cfp': 1
                    },
                    {
                        'description': '传输项目管理界面',
                        'move_type': 'X',
                        'data_group': '项目管理界面',
                        'data_attributes': '项目列表,筛选工具,指派操作,状态监控',
                        'context': '项目管理界面',
                        'cfp': 1
                    }
                ]
            )

            # 指派服务商
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="为待指派的项目单指派合适的服务商",
                trigger_event="管理员选择服务商并确认指派",
                process_name=f"{platform}指派项目服务商",
                subprocesses=[
                    {
                        'description': '接收服务商指派请求',
                        'move_type': 'E',
                        'data_group': '服务商指派请求',
                        'data_attributes': '项目ID,服务商ID,指派原因,管理员ID',
                        'context': '服务商指派请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取服务商资质验证',
                        'move_type': 'R',
                        'data_group': '服务商资质验证',
                        'data_attributes': '服务商ID,认证状态,服务范围,历史评价',
                        'context': '服务商资质验证',
                        'cfp': 1
                    },
                    {
                        'description': '修改项目指派状态',
                        'move_type': 'W',
                        'data_group': '项目指派状态',
                        'data_attributes': '项目ID,指派服务商,项目状态,指派时间',
                        'context': '项目指派状态',
                        'cfp': 1
                    },
                    {
                        'description': '存储指派操作记录',
                        'move_type': 'W',
                        'data_group': '指派操作记录',
                        'data_attributes': '操作ID,项目ID,服务商ID,指派人,操作时间',
                        'context': '指派操作记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输指派结果通知',
                        'move_type': 'X',
                        'data_group': '指派结果通知',
                        'data_attributes': '指派状态,项目详情,服务商联系方式,后续流程',
                        'context': '指派结果通知',
                        'cfp': 1
                    }
                ]
            )

            # FR-AG-WEB-003: 广告推广配置
            self.add_functional_process(
                platform=platform,
                functional_user="平台运营管理员",
                user_requirement="配置适老化改造的广告推广内容",
                trigger_event="管理员访问广告推广配置页面",
                process_name=f"{platform}配置广告推广",
                subprocesses=[
                    {
                        'description': '接收广告配置请求',
                        'move_type': 'E',
                        'data_group': '广告配置请求',
                        'data_attributes': '管理员ID,广告位ID,推广类型',
                        'context': '广告配置请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取可推广套餐列表',
                        'move_type': 'R',
                        'data_group': '可推广套餐列表',
                        'data_attributes': '套餐ID,套餐名称,推广价值,适用场景',
                        'context': '可推广套餐列表',
                        'cfp': 1
                    },
                    {
                        'description': '获取认证服务商列表',
                        'move_type': 'R',
                        'data_group': '认证服务商列表',
                        'data_attributes': '服务商ID,服务商名称,推广等级,认证状态',
                        'context': '认证服务商列表',
                        'cfp': 1
                    },
                    {
                        'description': '获取广告位配置',
                        'move_type': 'R',
                        'data_group': '广告位配置',
                        'data_attributes': '广告位ID,位置描述,展示规则,当前内容',
                        'context': '广告位配置',
                        'cfp': 1
                    },
                    {
                        'description': '存储广告推广设置',
                        'move_type': 'W',
                        'data_group': '广告推广设置',
                        'data_attributes': '广告位ID,推广内容,推广时间,展示优先级',
                        'context': '广告推广设置',
                        'cfp': 1
                    },
                    {
                        'description': '传输配置结果',
                        'move_type': 'X',
                        'data_group': '配置结果',
                        'data_attributes': '配置状态,生效时间,预览效果,推广计划',
                        'context': '配置结果',
                        'cfp': 1
                    }
                ]
            )
