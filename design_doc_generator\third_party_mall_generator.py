#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三方商城积分消费集成系统 COSMIC功能点生成器
"""

import os
import sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from base_cosmic_generator import BaseCosmicGenerator

class ThirdPartyMallGenerator(BaseCosmicGenerator):
    """第三方商城积分消费集成系统COSMIC功能点生成器"""
    
    def __init__(self):
        super().__init__(
            subsystem="智慧社区",
            module_level2="积分兑换", 
            module_level3="第三方商城集成"
        )
    
    def generate_functional_processes(self):
        """生成第三方商城集成的所有功能过程"""
        
        platforms = ["移动端", "PC端"]
        
        for platform in platforms:
            # 1. 用户跳转第三方商城
            self.add_functional_process(
                platform=platform,
                functional_user="用户",
                user_requirement="用户跳转第三方商城",
                trigger_event="用户点击商城链接",
                process_name=f"{platform}生成商城跳转链接",
                subprocesses=[
                    {
                        'description': '接收用户跳转商城请求',
                        'move_type': 'E',
                        'data_group': '用户跳转请求',
                        'data_attributes': '用户ID,跳转时间,设备类型',
                        'context': '跳转请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户基本数据',
                        'move_type': 'R', 
                        'data_group': '用户基本',
                        'data_attributes': '用户ID,用户名,积分余额,用户等级',
                        'context': '用户基本',
                        'cfp': 1
                    },
                    {
                        'description': '创建JWT认证令牌并传输',
                        'move_type': 'X',
                        'data_group': 'JWT令牌',
                        'data_attributes': '令牌内容,过期时间,用户标识,设备标识',
                        'context': 'JWT令牌',
                        'cfp': 1
                    },
                    {
                        'description': '存储用户跳转记录',
                        'move_type': 'W',
                        'data_group': '用户跳转记录',
                        'data_attributes': '用户ID,跳转时间,IP地址,用户代理,设备型号',
                        'context': '跳转记录',
                        'cfp': 1
                    }
                ]
            )
            
            # 2. 第三方商城查询用户积分
            self.add_functional_process(
                platform=platform,
                functional_user="第三方商城",
                user_requirement="第三方商城查询用户积分",
                trigger_event="第三方商城发起积分查询",
                process_name=f"{platform}检索用户积分余额",
                subprocesses=[
                    {
                        'description': '接收积分检索请求',
                        'move_type': 'E',
                        'data_group': '积分检索请求',
                        'data_attributes': '检索用户ID,检索时间,API令牌,请求来源',
                        'context': '积分检索请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户积分',
                        'move_type': 'R',
                        'data_group': '用户积分',
                        'data_attributes': '积分用户ID,总积分,可用积分,冻结积分,积分等级',
                        'context': '用户积分',
                        'cfp': 1
                    },
                    {
                        'description': '传输用户积分明细',
                        'move_type': 'X',
                        'data_group': '用户积分明细',
                        'data_attributes': '积分用户名,积分总数,积分可用数,积分冻结数,积分有效期',
                        'context': '用户积分明细',
                        'cfp': 1
                    }
                ]
            )
            
            # 3. 第三方商城积分消费回调
            self.add_functional_process(
                platform=platform,
                functional_user="第三方商城",
                user_requirement="第三方商城积分消费回调",
                trigger_event="第三方商城订单支付成功",
                process_name=f"{platform}处理积分消费扣除",
                subprocesses=[
                    {
                        'description': '接收积分消费回调请求',
                        'move_type': 'E',
                        'data_group': '积分消费回调',
                        'data_attributes': '用户ID,消费积分,订单ID,消费金额,商品名称,时间戳,签名',
                        'context': '积分消费回调',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户积分余额',
                        'move_type': 'R',
                        'data_group': '用户积分余额',
                        'data_attributes': '用户ID,可用积分,冻结积分',
                        'context': '用户积分余额',
                        'cfp': 1
                    },
                    {
                        'description': '修改用户积分余额',
                        'move_type': 'W',
                        'data_group': '用户积分修改',
                        'data_attributes': '用户ID,扣除积分数,修改后余额',
                        'context': '用户积分修改',
                        'cfp': 1
                    },
                    {
                        'description': '存储积分交易记录',
                        'move_type': 'W',
                        'data_group': '积分交易记录',
                        'data_attributes': '用户ID,积分变动,交易类型,订单ID,交易描述,交易时间',
                        'context': '积分交易记录',
                        'cfp': 1
                    },
                    {
                        'description': '存储消费记录',
                        'move_type': 'W',
                        'data_group': '消费记录',
                        'data_attributes': '用户ID,第三方订单ID,消费积分,消费金额,商品名称,消费时间',
                        'context': '消费记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输积分扣除输出',
                        'move_type': 'X',
                        'data_group': '积分扣除输出',
                        'data_attributes': '处理状态,扣除积分数,剩余积分,处理时间',
                        'context': '积分扣除输出',
                        'cfp': 1
                    }
                ]
            )
            
            # 4. 第三方商城月度消费统计查询
            self.add_functional_process(
                platform=platform,
                functional_user="第三方商城",
                user_requirement="第三方商城月度消费统计查询",
                trigger_event="每月1号系统定时触发",
                process_name=f"{platform}生成月度消费统计",
                subprocesses=[
                    {
                        'description': '接收月度统计检索请求',
                        'move_type': 'E',
                        'data_group': '月度统计检索请求',
                        'data_attributes': '检索月份,API令牌,检索时间',
                        'context': '月度统计检索请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取月度消费记录',
                        'move_type': 'R',
                        'data_group': '月度消费记录',
                        'data_attributes': '用户ID,消费次数,消费积分,消费金额,结算状态',
                        'context': '月度消费记录',
                        'cfp': 1
                    },
                    {
                        'description': '修改消费记录结算状态',
                        'move_type': 'W',
                        'data_group': '消费记录结算状态',
                        'data_attributes': '记录ID,结算状态,结算月份',
                        'context': '消费记录结算状态',
                        'cfp': 1
                    },
                    {
                        'description': '传输月度消费统计结果',
                        'move_type': 'X',
                        'data_group': '月度消费统计',
                        'data_attributes': '统计月份,总用户数,总订单数,总消费积分,总消费金额,用户消费明细',
                        'context': '月度消费统计',
                        'cfp': 1
                    }
                ]
            )
            
            # 5. 管理员查询消费记录
            self.add_functional_process(
                platform=platform,
                functional_user="管理员",
                user_requirement="管理员查询消费记录",
                trigger_event="管理员发起查询请求",
                process_name=f"{platform}检索用户消费记录",
                subprocesses=[
                    {
                        'description': '接收消费记录检索请求',
                        'move_type': 'E',
                        'data_group': '消费记录检索请求',
                        'data_attributes': '用户ID,检索时间范围,检索条件',
                        'context': '消费记录检索请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取用户消费记录',
                        'move_type': 'R',
                        'data_group': '用户消费记录',
                        'data_attributes': '用户ID,消费时间,消费积分,消费金额,商品名称,订单状态',
                        'context': '用户消费记录',
                        'cfp': 1
                    },
                    {
                        'description': '传输消费记录集合',
                        'move_type': 'X',
                        'data_group': '消费记录集合',
                        'data_attributes': '记录总数,消费记录明细,分页数据',
                        'context': '消费记录集合',
                        'cfp': 1
                    }
                ]
            )
            
            # 6. 管理员配置第三方商城参数
            self.add_functional_process(
                platform=platform,
                functional_user="管理员",
                user_requirement="管理员配置第三方商城参数",
                trigger_event="管理员修改配置参数",
                process_name=f"{platform}更新第三方商城参数",
                subprocesses=[
                    {
                        'description': '接收参数更新请求',
                        'move_type': 'E',
                        'data_group': '参数更新请求',
                        'data_attributes': '平台类型,API密钥,密钥密码,基础URL,参数内容',
                        'context': '参数更新请求',
                        'cfp': 1
                    },
                    {
                        'description': '获取当前参数',
                        'move_type': 'R',
                        'data_group': '当前参数',
                        'data_attributes': '平台类型,API密钥,基础URL,同步状态,参数数据',
                        'context': '当前参数',
                        'cfp': 1
                    },
                    {
                        'description': '存储更新后参数',
                        'move_type': 'W',
                        'data_group': '更新后参数',
                        'data_attributes': '平台类型,新API密钥,新基础URL,更新时间,操作人员',
                        'context': '更新后参数',
                        'cfp': 1
                    },
                    {
                        'description': '传输参数更新输出',
                        'move_type': 'X',
                        'data_group': '参数更新输出',
                        'data_attributes': '更新状态,更新时间,参数明细',
                        'context': '参数更新输出',
                        'cfp': 1
                    }
                ]
            )
