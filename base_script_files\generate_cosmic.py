#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一的COSMIC功能点生成接口
支持动态发现生成器、选择性生成、设计文档分析等功能
"""

import os
import sys
import argparse
from typing import List, Dict

# 添加design_doc_generator到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
design_doc_generator_path = os.path.join(parent_dir, 'design_doc_generator')
sys.path.append(design_doc_generator_path)

# 直接导入模块
import cosmic_generator_factory
import design_doc_analyzer

class CosmicGeneratorManager:
    """COSMIC功能点生成器管理器"""
    
    def __init__(self):
        self.factory = cosmic_generator_factory.CosmicGeneratorFactory()
    
    def list_available_generators(self) -> Dict[str, str]:
        """列出所有可用的生成器"""
        return self.factory.list_generators()
    
    def generate_single(self, generator_type: str, output_filename: str = None) -> str:
        """生成单个生成器的功能点"""
        return self.factory.generate_cosmic_points(generator_type, output_filename)
    
    def generate_multiple(self, generator_types: List[str]) -> List[str]:
        """生成多个生成器的功能点"""
        generated_files = []
        
        for generator_type in generator_types:
            try:
                output_file = self.factory.generate_cosmic_points(generator_type)
                generated_files.append(output_file)
                print(f"✅ {generator_type}: {output_file}")
            except Exception as e:
                print(f"❌ {generator_type}: {e}")
        
        return generated_files
    
    def generate_all(self) -> List[str]:
        """生成所有可用生成器的功能点"""
        generators = self.factory.list_generators()
        return self.generate_multiple(list(generators.keys()))
    
    def analyze_design_doc(self, doc_path: str, generator_name: str = None) -> str:
        """分析设计文档并生成对应的生成器"""
        
        analyzer = design_doc_analyzer.DesignDocAnalyzer()
        
        # 分析文档
        result = analyzer.analyze_markdown_doc(doc_path)
        
        # 生成生成器名称
        if not generator_name:
            # 从文档路径生成名称
            base_name = os.path.splitext(os.path.basename(doc_path))[0]
            generator_name = base_name.replace('_', '').replace('-', '').lower()
        
        # 生成类名
        class_name = ''.join(word.capitalize() for word in generator_name.split('_')) + 'Generator'
        
        # 生成生成器脚本
        script_path = os.path.join('design_doc_generator', f'{generator_name}_generator.py')
        analyzer.generate_generator_script(script_path, class_name)
        
        # 保存分析结果
        analysis_path = f'{generator_name}_analysis.json'
        analyzer.save_analysis_result(analysis_path)
        
        return script_path, analysis_path
    
    def interactive_select(self) -> List[str]:
        """交互式选择生成器"""
        
        generators = self.list_available_generators()
        
        if not generators:
            print("❌ 没有找到任何生成器")
            return []
        
        print("📋 可用的COSMIC功能点生成器:")
        generator_list = list(generators.items())
        
        for i, (name, description) in enumerate(generator_list, 1):
            print(f"  {i}. {name}: {description}")
        
        print(f"  {len(generator_list) + 1}. 全部生成")
        print("  0. 退出")
        
        while True:
            try:
                choice = input("\n请选择要生成的功能点 (可输入多个数字，用逗号分隔): ").strip()
                
                if choice == '0':
                    return []
                
                if choice == str(len(generator_list) + 1):
                    return list(generators.keys())
                
                # 解析选择
                choices = [int(x.strip()) for x in choice.split(',')]
                selected_generators = []
                
                for c in choices:
                    if 1 <= c <= len(generator_list):
                        generator_name = generator_list[c-1][0]
                        selected_generators.append(generator_name)
                    else:
                        print(f"⚠️ 无效选择: {c}")
                
                if selected_generators:
                    return selected_generators
                else:
                    print("❌ 没有有效的选择")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n👋 已取消")
                return []
    
    def post_generation_actions(self, generated_files: List[str], auto_process: bool = False):
        """生成后的操作建议或自动处理"""

        if not generated_files:
            return

        print(f"\n🎉 成功生成 {len(generated_files)} 个COSMIC功能点文件!")

        for file in generated_files:
            print(f"  📄 {file}")

        if auto_process:
            print(f"\n🔧 正在自动执行后续处理...")
            self._auto_process_files(generated_files)
        else:
            print(f"\n🔧 后续操作建议:")
            print(f"1. 检查功能点:")
            for file in generated_files:
                print(f"   python cosmic_checker.py --file {file}")

            print(f"\n2. 转换为Excel:")
            for file in generated_files:
                print(f"   python csv_to_xlsx_merger.py --input {file}")

            print(f"\n3. 生成统计报告:")
            for file in generated_files:
                print(f"   python cosmic_stats.py {file}")

    def _auto_process_files(self, generated_files: List[str]):
        """自动处理生成的文件"""
        import subprocess

        for file in generated_files:
            print(f"\n📋 处理文件: {file}")

            # 1. 检查功能点
            print("  🔍 检查COSMIC功能点规范...")
            try:
                result = subprocess.run(['python', 'cosmic_checker.py', '--file', file],
                                      capture_output=True, text=True, encoding='utf-8')
                if result.returncode == 0:
                    print("  ✅ 功能点检查完成")
                else:
                    print("  ⚠️ 检查过程中发现问题")
                    print(result.stdout)
            except Exception as e:
                print(f"  ❌ 检查失败: {e}")

            # 2. 转换为Excel
            print("  📊 转换为Excel格式...")
            try:
                result = subprocess.run(['python', 'csv_to_xlsx_merger.py', '--input', file],
                                      capture_output=True, text=True, encoding='utf-8')
                if result.returncode == 0:
                    print("  ✅ Excel文件生成完成")
                else:
                    print("  ❌ Excel转换失败")
                    print(result.stdout)
            except Exception as e:
                print(f"  ❌ Excel转换失败: {e}")

            # 3. 生成统计报告
            print("  📈 生成统计报告...")
            try:
                result = subprocess.run(['python', 'cosmic_stats.py', file],
                                      capture_output=True, text=True, encoding='utf-8')
                if result.returncode == 0:
                    print("  ✅ 统计报告生成完成")
                    # 显示统计结果
                    if result.stdout.strip():
                        print("  📊 统计结果:")
                        for line in result.stdout.strip().split('\n'):
                            print(f"    {line}")
                else:
                    print("  ❌ 统计报告生成失败")
            except Exception as e:
                print(f"  ❌ 统计报告生成失败: {e}")

            print(f"  🎉 {file} 处理完成!")

        print(f"\n✨ 所有文件处理完成!")

        # 显示生成的文件
        print(f"\n📁 生成的文件:")
        for file in generated_files:
            base_name = file.replace('.csv', '')
            print(f"  📄 {file} (CSV格式)")
            print(f"  📊 {base_name}.xlsx (Excel格式)")
        print()


def main():
    """主函数"""
    
    parser = argparse.ArgumentParser(description="统一的COSMIC功能点生成接口")
    parser.add_argument("--list", "-l", action="store_true", help="列出所有可用的生成器")
    parser.add_argument("--type", "-t", help="指定生成器类型")
    parser.add_argument("--types", help="指定多个生成器类型，用逗号分隔")
    parser.add_argument("--all", "-a", action="store_true", help="生成所有可用生成器的功能点")
    parser.add_argument("--interactive", "-i", action="store_true", help="交互式选择生成器")
    parser.add_argument("--output", "-o", help="输出文件名")
    parser.add_argument("--analyze-doc", help="分析设计文档并生成对应的生成器")
    parser.add_argument("--generator-name", help="分析文档时指定生成器名称")
    
    args = parser.parse_args()
    
    manager = CosmicGeneratorManager()
    
    try:
        # 列出可用生成器
        if args.list:
            generators = manager.list_available_generators()
            print("📋 可用的COSMIC功能点生成器:")
            for name, description in generators.items():
                print(f"  {name}: {description}")
            return
        
        # 分析设计文档
        if args.analyze_doc:
            print(f"📖 正在分析设计文档: {args.analyze_doc}")
            script_path, analysis_path = manager.analyze_design_doc(args.analyze_doc, args.generator_name)
            print(f"✅ 分析完成!")
            print(f"📄 生成器脚本: {script_path}")
            print(f"📊 分析结果: {analysis_path}")
            print(f"\n💡 提示: 重新运行 --list 命令查看新生成的生成器")
            return
        
        generated_files = []
        
        # 生成所有
        if args.all:
            generated_files = manager.generate_all()
        
        # 交互式选择
        elif args.interactive:
            selected_generators = manager.interactive_select()
            if selected_generators:
                generated_files = manager.generate_multiple(selected_generators)
        
        # 生成多个指定类型
        elif args.types:
            generator_types = [t.strip() for t in args.types.split(',')]
            generated_files = manager.generate_multiple(generator_types)
        
        # 生成单个指定类型
        elif args.type:
            output_file = manager.generate_single(args.type, args.output)
            generated_files = [output_file]
        
        else:
            # 默认交互式选择
            selected_generators = manager.interactive_select()
            if selected_generators:
                generated_files = manager.generate_multiple(selected_generators)
        
        # 后续操作建议
        manager.post_generation_actions(generated_files)
        
    except KeyboardInterrupt:
        print("\n👋 已取消操作")
    except Exception as e:
        print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    main()
